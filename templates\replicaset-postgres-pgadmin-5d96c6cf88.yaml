﻿apiVersion: apps/v1
kind: ReplicaSet
metadata:
  annotations:
    deployment.kubernetes.io/desired-replicas: "1"
    deployment.kubernetes.io/max-replicas: "2"
    deployment.kubernetes.io/revision: "1"
  labels:
    app: pgadmin
    pod-template-hash: 5d96c6cf88
  name: pgadmin-5d96c6cf88
  namespace: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pgadmin
      pod-template-hash: 5d96c6cf88
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: pgadmin
        pod-template-hash: 5d96c6cf88
    spec:
      containers:
      - envFrom:
        - secretRef:
            name: pgadmin-secret
        image: dpage/pgadmin4:8.5
        imagePullPolicy: IfNotPresent
        name: pgadmin
        ports:
        - containerPort: 80
          protocol: TCP
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /var/lib/pgadmin
          name: pgadmin-storage
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      volumes:
      - name: pgadmin-storage
