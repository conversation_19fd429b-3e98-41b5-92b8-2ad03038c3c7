🎉 N8N Stack has been deployed successfully!

📋 Deployment Summary:
- N8N Main: {{ .Values.n8n.main.replicaCount }} replica(s)
- N8N Worker: {{ .Values.n8n.worker.replicaCount }} replica(s)
- PostgreSQL: {{ .Values.postgresql.replicaCount }} replica(s)
- Redis: {{ .Values.redis.replicaCount }} replica(s)
{{- if .Values.pgadmin.enabled }}
- PgAdmin: {{ .Values.pgadmin.replicaCount }} replica(s)
{{- end }}

🌐 Access URLs:

{{- if .Values.n8n.ingress.admin.enabled }}
N8N Admin Interface:
{{- range $host := .Values.n8n.ingress.admin.hosts }}
  {{- range .paths }}
  http{{ if $.Values.n8n.ingress.admin.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}
  Username: {{ .Values.n8n.auth.user }}
  Password: <value of secrets.n8nAuthPassword>
{{- else }}
N8N Admin Interface (via port-forward):
  kubectl --namespace {{ .Release.Namespace }} port-forward svc/{{ include "mychart.fullname" . }}-n8n 5678:5678
  Then visit: http://localhost:5678
{{- end }}

{{- if .Values.n8n.ingress.webhook.enabled }}
N8N Webhook URL:
{{- range $host := .Values.n8n.ingress.webhook.hosts }}
  {{- range .paths }}
  http{{ if $.Values.n8n.ingress.webhook.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}
{{- end }}

{{- if and .Values.pgadmin.enabled .Values.pgadmin.ingress.enabled }}
PgAdmin Interface:
{{- range $host := .Values.pgadmin.ingress.hosts }}
  {{- range .paths }}
  http{{ if $.Values.pgadmin.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}
  Email: <value of secrets.pgadminEmail>
  Password: <value of secrets.pgadminPassword>
{{- else if .Values.pgadmin.enabled }}
PgAdmin Interface (via port-forward):
  kubectl --namespace {{ .Release.Namespace }} port-forward svc/{{ include "mychart.fullname" . }}-pgadmin 8080:80
  Then visit: http://localhost:8080
{{- end }}

🔍 Monitoring Commands:
  # Check all pods
  kubectl get pods --namespace {{ .Release.Namespace }} -l app.kubernetes.io/instance={{ .Release.Name }}

  # Check N8N logs
  kubectl logs --namespace {{ .Release.Namespace }} -l app.kubernetes.io/component=n8n-main

  # Check PostgreSQL logs
  kubectl logs --namespace {{ .Release.Namespace }} -l app.kubernetes.io/component=postgresql

📚 For more information, see the README.md file or visit https://docs.n8n.io/
