﻿apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  annotations:
    pv.kubernetes.io/bind-completed: "yes"
    pv.kubernetes.io/bound-by-controller: "yes"
    volume.beta.kubernetes.io/storage-provisioner: blockvolume.csi.oraclecloud.com
    volume.kubernetes.io/selected-node: 10.0.10.103
    volume.kubernetes.io/storage-provisioner: blockvolume.csi.oraclecloud.com
  labels:
    app.kubernetes.io/component: primary
    app.kubernetes.io/instance: postgres
    app.kubernetes.io/name: postgresql
  name: data-postgres-postgresql-0
  namespace: postgres
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 8Gi
  storageClassName: oci-bv
  volumeName: csi-f1c3b94d-1065-4d2f-a9f7-a907289033ad
