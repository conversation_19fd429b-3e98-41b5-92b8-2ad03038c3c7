﻿{{- if .Values.pgadmin.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "mychart.fullname" . }}-pgadmin
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: pgadmin
spec:
  replicas: {{ .Values.pgadmin.replicaCount }}
  selector:
    matchLabels:
      {{- include "mychart.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: pgadmin
  template:
    metadata:
      labels:
        {{- include "mychart.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: pgadmin
    spec:
      containers:
      - name: pgadmin
        image: "{{ .Values.pgadmin.image.repository }}:{{ .Values.pgadmin.image.tag }}"
        imagePullPolicy: {{ .Values.pgadmin.image.pullPolicy }}
        ports:
        - name: http
          containerPort: 80
          protocol: TCP
        envFrom:
        - secretRef:
            name: {{ include "mychart.fullname" . }}-pgadmin-secrets
        {{- with .Values.pgadmin.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.pgadmin.livenessProbe }}
        livenessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.pgadmin.readinessProbe }}
        readinessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- if .Values.pgadmin.persistence.enabled }}
        volumeMounts:
        - name: pgadmin-storage
          mountPath: /var/lib/pgadmin
        {{- end }}
      {{- with .Values.pgadmin.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.pgadmin.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.pgadmin.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.pgadmin.persistence.enabled }}
      volumes:
      - name: pgadmin-storage
        persistentVolumeClaim:
          claimName: {{ include "mychart.fullname" . }}-pgadmin-pvc
      {{- end }}
{{- end }}
