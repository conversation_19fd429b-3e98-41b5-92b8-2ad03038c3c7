﻿apiVersion: v1
kind: Service
metadata:
  annotations:
    meta.helm.sh/release-name: cert-manager
    meta.helm.sh/release-namespace: cert-manager
  labels:
    app: cainjector
    app.kubernetes.io/component: cainjector
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: cainjector
    app.kubernetes.io/version: v1.18.1
    helm.sh/chart: cert-manager-v1.18.1
  name: cert-manager-cainjector
  namespace: cert-manager
spec:
  clusterIP: ***********
  clusterIPs:
  - ***********
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: http-metrics
    port: 9402
  selector:
    app.kubernetes.io/component: cainjector
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cainjector
