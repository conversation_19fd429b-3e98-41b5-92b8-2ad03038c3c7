apiVersion: v2
name: n8n-stack
description: A complete N8N workflow automation platform with PostgreSQL database, Redis queue, and PgAdmin management interface

type: application

# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 1.0.0

# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
# It is recommended to use it with quotes.
appVersion: "1.0.0"

keywords:
  - n8n
  - workflow
  - automation
  - postgresql
  - redis
  - pgadmin

home: https://n8n.io
sources:
  - https://github.com/n8n-io/n8n

maintainers:
  - name: Chart Maintainer
    email: <EMAIL>
