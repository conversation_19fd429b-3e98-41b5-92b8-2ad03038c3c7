﻿apiVersion: apps/v1
kind: ReplicaSet
metadata:
  annotations:
    deployment.kubernetes.io/desired-replicas: "1"
    deployment.kubernetes.io/max-replicas: "2"
    deployment.kubernetes.io/revision: "1"
  labels:
    app: redis
    pod-template-hash: "7574458548"
  name: redis-7574458548
  namespace: n8n
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
      pod-template-hash: "7574458548"
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: redis
        pod-template-hash: "7574458548"
    spec:
      containers:
      - image: redis:6.2
        imagePullPolicy: IfNotPresent
        name: redis
        ports:
        - containerPort: 6379
          protocol: TCP
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
