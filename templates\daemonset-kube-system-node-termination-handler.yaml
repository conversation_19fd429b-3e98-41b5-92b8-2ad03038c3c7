﻿apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    deprecated.daemonset.template.generation: "1"
  labels:
    k8s-app: node-termination-handler
  name: node-termination-handler
  namespace: kube-system
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      k8s-app: node-termination-handler
  template:
    metadata:
      annotations:
        version_hash: "1945130711"
      creationTimestamp: null
      labels:
        k8s-app: node-termination-handler
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-role.kubernetes.io/virtual-node
                operator: DoesNotExist
      containers:
      - env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-node-termination-handler@sha256:4627130648f2b5d41ac982c792ec4511efcc45e82317f37f5a4d1a670312e4ba
        imagePullPolicy: IfNotPresent
        name: node-termination-handler
        resources:
          limits:
            cpu: 128m
            memory: 256Mi
          requests:
            cpu: 10m
            memory: 50Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      hostNetwork: true
      nodeSelector:
        oci.oraclecloud.com/oke-is-preemptible: "true"
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      serviceAccount: node-termination-handler
      serviceAccountName: node-termination-handler
      terminationGracePeriodSeconds: 30
      tolerations:
      - key: CriticalAddonsOnly
        operator: Exists
      - operator: Exists
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
