﻿apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    deprecated.daemonset.template.generation: "1"
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    k8s-app: nvidia-gpu-device-plugin
  name: nvidia-gpu-device-plugin
  namespace: kube-system
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      k8s-app: nvidia-gpu-device-plugin
  template:
    metadata:
      annotations:
        scheduler.alpha.kubernetes.io/critical-pod: ""
        version_hash: "-1664424844"
      creationTimestamp: null
      labels:
        k8s-app: nvidia-gpu-device-plugin
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: nvidia.com/gpu
                operator: In
                values:
                - "true"
              - key: oci.oraclecloud.com/disable-gpu-device-plugin
                operator: NotIn
                values:
                - "true"
              - key: node-role.kubernetes.io/virtual-node
                operator: DoesNotExist
            - matchExpressions:
              - key: beta.kubernetes.io/instance-type
                operator: In
                values:
                - BM.GPU2.2
                - BM.GPU3.8
                - BM.GPU4.8
                - VM.GPU2.1
                - VM.GPU3.1
                - VM.GPU3.2
                - VM.GPU3.4
                - BM.GPU.T1.2
                - BM.GPU.T1-2.4
                - BM.GPU.A100-v2.8
                - BM.GPU.GM4.8
                - BM.GPU.A10.4
                - BM.GPU.GU1.4
                - VM.GPU.A10.1
                - VM.GPU.GU1.1
                - VM.GPU.A10.2
                - VM.GPU.GU1.2
                - BM.GPU.B4.8
                - BM.GPU.H100.8
                - BM.GPU.L40S.4
                - VM.GPU.L40S.1
                - VM.GPU.L40S.2
                - VM.GPU.A100.40G.1
                - VM.GPU.A100.40G.2
                - VM.GPU.A100.40G.4
                - VM.GPU.A100.40G.8
                - VM.GPU.A100.B40G.1
                - VM.GPU.A100.B40G.2
                - VM.GPU.A100.B40G.4
                - VM.GPU.A100.80G.1
                - VM.GPU.A100.80G.2
                - VM.GPU.A100.80G.4
                - VM.GPU.A100.80G.8
                - BM.GPU.L40S-NC.4
                - BM.GPU.H100T.8
              - key: oci.oraclecloud.com/disable-gpu-device-plugin
                operator: NotIn
                values:
                - "true"
              - key: node-role.kubernetes.io/virtual-node
                operator: DoesNotExist
      containers:
      - command:
        - nvidia-device-plugin
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-k8s-device-plugin@sha256:0e3c25b170348a55cac61f9768793b51e1a89b153e5ae00e7186911246ada326
        imagePullPolicy: IfNotPresent
        name: nvidia-gpu-device-plugin
        resources:
          limits:
            cpu: 50m
            memory: 200Mi
          requests:
            cpu: 50m
            memory: 200Mi
        securityContext:
          privileged: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /var/lib/kubelet/device-plugins
          name: device-plugin
        - mountPath: /dev
          name: dev
        - mountPath: /config
          name: nvidia-plugin-config-volume
          readOnly: true
      dnsPolicy: ClusterFirst
      hostNetwork: true
      hostPID: true
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      serviceAccount: oke-nvidia-device-plugin
      serviceAccountName: oke-nvidia-device-plugin
      terminationGracePeriodSeconds: 30
      tolerations:
      - effect: NoSchedule
        operator: Exists
      - effect: NoSchedule
        key: nvidia.com/gpu
        operator: Exists
      volumes:
      - hostPath:
          path: /var/lib/kubelet/device-plugins
          type: ""
        name: device-plugin
      - hostPath:
          path: /dev
          type: ""
        name: dev
      - configMap:
          defaultMode: 420
          name: nvidia-device-plugin-config
          optional: true
        name: nvidia-plugin-config-volume
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
