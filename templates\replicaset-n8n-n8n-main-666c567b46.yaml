﻿apiVersion: apps/v1
kind: ReplicaSet
metadata:
  annotations:
    deployment.kubernetes.io/desired-replicas: "1"
    deployment.kubernetes.io/max-replicas: "2"
    deployment.kubernetes.io/revision: "1"
  labels:
    app: n8n-main
    pod-template-hash: 666c567b46
  name: n8n-main-666c567b46
  namespace: n8n
spec:
  replicas: 1
  selector:
    matchLabels:
      app: n8n-main
      pod-template-hash: 666c567b46
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: "2025-06-27T22:58:07+02:00"
      creationTimestamp: null
      labels:
        app: n8n-main
        pod-template-hash: 666c567b46
    spec:
      containers:
      - env:
        - name: DB_TYPE
          value: postgresdb
        - name: DB_POSTGRESDB_HOST
          value: my-postgres-postgresql.postgres.svc.cluster.local
        - name: DB_POSTGRESDB_PORT
          value: "5432"
        - name: DB_POSTGRESDB_DATABASE
          value: n8n
        - name: DB_POSTGRESDB_USER
          value: n8n
        - name: DB_POSTGRESDB_PASSWORD
          value: strongpassword
        - name: QUEUE_MODE_ENABLED
          value: "true"
        - name: N8N_REDIS_HOST
          value: redis-service
        - name: N8N_BASIC_AUTH_ACTIVE
          value: "true"
        - name: N8N_BASIC_AUTH_USER
          value: admin
        - name: N8N_BASIC_AUTH_PASSWORD
          value: adminpassword
        - name: N8N_EDITOR_BASE_URL
          value: https://n8n-admin.locke.cz
        - name: WEBHOOK_URL
          value: https://n8n-webhook.locke.cz
        image: n8nio/n8n:latest
        imagePullPolicy: Always
        name: n8n
        ports:
        - containerPort: 5678
          protocol: TCP
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
