﻿apiVersion: v1
kind: Service
metadata:
  annotations:
    prometheus.io/port: "9400"
    prometheus.io/scrape: "true"
  labels:
    k8s-app: oke-nvidia-dcgm-exporter
  name: oke-nvidia-dcgm-exporter
  namespace: kube-system
spec:
  clusterIP: ************
  clusterIPs:
  - ************
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: oke-nvidia-dcgm-exporter
    port: 9400
  selector:
    k8s-app: oke-nvidia-dcgm-exporter
