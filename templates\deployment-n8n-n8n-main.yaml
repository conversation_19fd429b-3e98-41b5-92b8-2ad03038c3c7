﻿apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "mychart.fullname" . }}-n8n-main
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: n8n-main
spec:
  replicas: {{ .Values.n8n.main.replicaCount }}
  selector:
    matchLabels:
      {{- include "mychart.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: n8n-main
  template:
    metadata:
      labels:
        {{- include "mychart.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: n8n-main
    spec:
      containers:
      - name: n8n
        image: "{{ .Values.n8n.main.image.repository }}:{{ .Values.n8n.main.image.tag }}"
        imagePullPolicy: {{ .Values.n8n.main.image.pullPolicy }}
        ports:
        - name: http
          containerPort: 5678
          protocol: TCP
        env:
        - name: DB_TYPE
          value: {{ .Values.n8n.database.type }}
        - name: DB_POSTGRESDB_HOST
          value: {{ include "mychart.fullname" . }}-postgresql.{{ .Release.Namespace }}.svc.cluster.local
        - name: DB_POSTGRESDB_PORT
          value: "{{ .Values.postgresql.service.port }}"
        - name: DB_POSTGRESDB_DATABASE
          value: {{ .Values.n8n.database.name }}
        - name: DB_POSTGRESDB_USER
          value: {{ .Values.n8n.database.user }}
        - name: DB_POSTGRESDB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ include "mychart.fullname" . }}-n8n-secrets
              key: db-password
        - name: QUEUE_MODE_ENABLED
          value: "{{ .Values.n8n.queue.enabled }}"
        - name: N8N_REDIS_HOST
          value: {{ include "mychart.fullname" . }}-redis.{{ .Release.Namespace }}.svc.cluster.local
        - name: N8N_BASIC_AUTH_ACTIVE
          value: "{{ .Values.n8n.auth.enabled }}"
        - name: N8N_BASIC_AUTH_USER
          value: {{ .Values.n8n.auth.user }}
        - name: N8N_BASIC_AUTH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ include "mychart.fullname" . }}-n8n-secrets
              key: auth-password
        - name: N8N_EDITOR_BASE_URL
          value: {{ .Values.n8n.editorBaseUrl }}
        - name: WEBHOOK_URL
          value: {{ .Values.n8n.webhookUrl }}
        {{- with .Values.n8n.main.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.n8n.main.livenessProbe }}
        livenessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.n8n.main.readinessProbe }}
        readinessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- with .Values.n8n.main.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.n8n.main.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.n8n.main.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
