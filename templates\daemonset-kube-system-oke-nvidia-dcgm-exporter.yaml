﻿apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    deprecated.daemonset.template.generation: "1"
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
    k8s-app: oke-nvidia-dcgm-exporter
  name: oke-nvidia-dcgm-exporter
  namespace: kube-system
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      k8s-app: oke-nvidia-dcgm-exporter
  template:
    metadata:
      annotations:
        scheduler.alpha.kubernetes.io/critical-pod: ""
        version_hash: "-1664424844"
      creationTimestamp: null
      labels:
        k8s-app: oke-nvidia-dcgm-exporter
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: nvidia.com/gpu
                operator: In
                values:
                - "true"
              - key: oci.oraclecloud.com/disable-gpu-device-plugin
                operator: NotIn
                values:
                - "true"
              - key: node-role.kubernetes.io/virtual-node
                operator: DoesNotExist
            - matchExpressions:
              - key: beta.kubernetes.io/instance-type
                operator: In
                values:
                - BM.GPU2.2
                - BM.GPU3.8
                - BM.GPU4.8
                - VM.GPU2.1
                - VM.GPU3.1
                - VM.GPU3.2
                - VM.GPU3.4
                - BM.GPU.T1.2
                - BM.GPU.T1-2.4
                - BM.GPU.A100-v2.8
                - BM.GPU.GM4.8
                - BM.GPU.A10.4
                - BM.GPU.GU1.4
                - VM.GPU.A10.1
                - VM.GPU.GU1.1
                - VM.GPU.A10.2
                - VM.GPU.GU1.2
                - BM.GPU.B4.8
                - BM.GPU.H100.8
                - BM.GPU.L40S.4
                - VM.GPU.L40S.1
                - VM.GPU.L40S.2
                - VM.GPU.A100.40G.1
                - VM.GPU.A100.40G.2
                - VM.GPU.A100.40G.4
                - VM.GPU.A100.40G.8
                - VM.GPU.A100.B40G.1
                - VM.GPU.A100.B40G.2
                - VM.GPU.A100.B40G.4
                - VM.GPU.A100.80G.1
                - VM.GPU.A100.80G.2
                - VM.GPU.A100.80G.4
                - VM.GPU.A100.80G.8
                - BM.GPU.L40S-NC.4
                - BM.GPU.H100T.8
              - key: oci.oraclecloud.com/disable-gpu-device-plugin
                operator: NotIn
                values:
                - "true"
              - key: node-role.kubernetes.io/virtual-node
                operator: DoesNotExist
      containers:
      - command:
        - dcgm-exporter
        env:
        - name: NVIDIA_VISIBLE_DEVICES
          value: all
        - name: NVIDIA_DRIVER_CAPABILITIES
          value: utility
        - name: DCGM_EXPORTER_LISTEN
          value: :9400
        - name: DCGM_EXPORTER_KUBERNETES
          value: "true"
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-nvidia-dcgm-exporter@sha256:b1ccc3b0cc3cd5575e0f65ef46084d6a999d696ffe92eb6c7a09e6a485f423d1
        imagePullPolicy: IfNotPresent
        name: oke-nvidia-dcgm-exporter
        ports:
        - containerPort: 9400
          name: metrics
          protocol: TCP
        securityContext:
          privileged: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /var/lib/kubelet/device-plugins
          name: device-plugin
        - mountPath: /dev
          name: dev
      dnsPolicy: ClusterFirst
      hostNetwork: true
      hostPID: true
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      serviceAccount: oke-nvidia-dcgm-exporter
      serviceAccountName: oke-nvidia-dcgm-exporter
      terminationGracePeriodSeconds: 30
      tolerations:
      - effect: NoSchedule
        operator: Exists
      - effect: NoSchedule
        key: nvidia.com/gpu
        operator: Exists
      volumes:
      - hostPath:
          path: /var/lib/kubelet/device-plugins
          type: ""
        name: device-plugin
      - hostPath:
          path: /dev
          type: ""
        name: dev
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
