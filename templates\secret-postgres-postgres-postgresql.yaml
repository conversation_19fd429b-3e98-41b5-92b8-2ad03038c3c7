﻿apiVersion: v1
data:
  password: c3Ryb25ncGFzc3dvcmQ=
  postgres-password: b3RFV2ZoZGFydA==
kind: Secret
metadata:
  annotations:
    meta.helm.sh/release-name: postgres
    meta.helm.sh/release-namespace: postgres
  labels:
    app.kubernetes.io/instance: postgres
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/version: 17.5.0
    helm.sh/chart: postgresql-16.7.14
  name: postgres-postgresql
  namespace: postgres
type: Opaque
