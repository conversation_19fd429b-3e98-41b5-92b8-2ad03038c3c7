﻿apiVersion: v1
data:
  ip6tables: |-
    #!/bin/sh
    if [ -x /host/sbin/ip6tables ]; then
    chroot /host /sbin/ip6tables "$@"
    elif [ -x /host/usr/local/sbin/ip6tables ]; then
    chroot /host /usr/local/sbin/ip6tables "$@"
    elif [ -x /host/bin/ip6tables ]; then
    chroot /host /bin/ip6tables "$@"
    elif [ -x /host/usr/local/bin/ip6tables ]; then
    chroot /host /usr/local/bin/ip6tables "$@"
    else
    chroot /host ip6tables "$@"
    fi
  ip6tables-restore: |-
    #!/bin/sh
    if [ -x /host/sbin/ip6tables-restore ]; then
    chroot /host /sbin/ip6tables-restore "$@"
    elif [ -x /host/usr/local/sbin/ip6tables-restore ]; then
    chroot /host /usr/local/sbin/ip6tables-restore "$@"
    elif [ -x /host/bin/ip6tables-restore ]; then
    chroot /host /bin/ip6tables-restore "$@"
    elif [ -x /host/usr/local/bin/ip6tables-restore ]; then
    chroot /host /usr/local/bin/ip6tables-restore "$@"
    else
    chroot /host ip6tables-restore "$@"
    fi
  ip6tables-save: |-
    #!/bin/sh
    if [ -x /host/sbin/ip6tables-save ]; then
    chroot /host /sbin/ip6tables-save "$@"
    elif [ -x /host/usr/local/sbin/ip6tables-save ]; then
    chroot /host /usr/local/sbin/ip6tables-save "$@"
    elif [ -x /host/bin/ip6tables-save ]; then
    chroot /host /bin/ip6tables-save "$@"
    elif [ -x /host/usr/local/bin/ip6tables-save ]; then
    chroot /host /usr/local/bin/ip6tables-save "$@"
    else
    chroot /host ip6tables-save "$@"
    fi
  iptables: |-
    #!/bin/sh
    if [ -x /host/sbin/iptables ]; then
    chroot /host /sbin/iptables "$@"
    elif [ -x /host/usr/local/sbin/iptables ]; then
    chroot /host /usr/local/sbin/iptables "$@"
    elif [ -x /host/bin/iptables ]; then
    chroot /host /bin/iptables "$@"
    elif [ -x /host/usr/local/bin/iptables ]; then
    chroot /host /usr/local/bin/iptables "$@"
    else
    chroot /host iptables "$@"
    fi
  iptables-restore: |-
    #!/bin/sh
    if [ -x /host/sbin/iptables-restore ]; then
    chroot /host /sbin/iptables-restore "$@"
    elif [ -x /host/usr/local/sbin/iptables-restore ]; then
    chroot /host /usr/local/sbin/iptables-restore "$@"
    elif [ -x /host/bin/iptables-restore ]; then
    chroot /host /bin/iptables-restore "$@"
    elif [ -x /host/usr/local/bin/iptables-restore ]; then
    chroot /host /usr/local/bin/iptables-restore "$@"
    else
    chroot /host iptables-restore "$@"
    fi
  iptables-save: |-
    #!/bin/sh
    if [ -x /host/sbin/iptables-save ]; then
    chroot /host /sbin/iptables-save "$@"
    elif [ -x /host/usr/local/sbin/iptables-save ]; then
    chroot /host /usr/local/sbin/iptables-save "$@"
    elif [ -x /host/bin/iptables-save ]; then
    chroot /host /bin/iptables-save "$@"
    elif [ -x /host/usr/local/bin/iptables-save ]; then
    chroot /host /usr/local/bin/iptables-save "$@"
    else
    chroot /host iptables-save "$@"
    fi
kind: ConfigMap
metadata:
  name: oci-iptables-kubeproxy
  namespace: kube-system
