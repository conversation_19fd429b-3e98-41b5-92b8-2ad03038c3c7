﻿apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare
    kubernetes.io/timestamp: 06/30/2025 11:16:50
  name: n8n-webhook-ingress
  namespace: n8n
spec:
  ingressClassName: nginx
  rules:
  - host: n8n-webhook.locke.cz
    http:
      paths:
      - backend:
          service:
            name: n8n-service
            port:
              number: 5678
        path: /webhook/
        pathType: Prefix
  tls:
  - hosts:
    - n8n-webhook.locke.cz
    secretName: n8n-webhook-tls
