﻿apiVersion: v1
data:
  tls.crt: 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
  tls.key: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/alt-names: dashboard.locke.cz
    cert-manager.io/certificate-name: dashboard-tls
    cert-manager.io/common-name: dashboard.locke.cz
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: cert-manager.io
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: letsencrypt-cloudflare
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
  name: dashboard-tls
  namespace: kubernetes-dashboard
type: kubernetes.io/tls
