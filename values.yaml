# Default values for n8n-stack.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# Global settings
global:
  storageClass: ""

# Override chart name
nameOverride: ""
fullnameOverride: ""

# Image pull secrets for private registries
imagePullSecrets: []

# Service account settings
serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

# Secrets configuration - REQUIRED VALUES
# These must be provided during installation
secrets:
  # N8N database password
  n8nDbPassword: ""
  # N8N basic auth password
  n8nAuthPassword: ""
  # PostgreSQL user password
  postgresqlPassword: ""
  # PostgreSQL root password
  postgresqlRootPassword: ""
  # Redis password (if auth enabled)
  redisPassword: ""
  # PgAdmin email and password
  pgadminEmail: ""
  pgadminPassword: ""

# N8N Configuration
n8n:
  # Database configuration
  database:
    type: "postgresdb"
    name: "n8n"
    user: "n8n"

  # Queue configuration
  queue:
    enabled: true

  # Authentication configuration
  auth:
    enabled: true
    user: "admin"

  # URLs - MUST be configured for your domain
  editorBaseUrl: "https://n8n-admin.example.com"
  webhookUrl: "https://n8n-webhook.example.com"

  # N8N Main instance
  main:
    replicaCount: 1
    image:
      repository: "n8nio/n8n"
      tag: "latest"
      pullPolicy: IfNotPresent

    resources: {}
      # limits:
      #   cpu: 500m
      #   memory: 1Gi
      # requests:
      #   cpu: 250m
      #   memory: 512Mi

    livenessProbe:
      httpGet:
        path: /healthz
        port: 5678
      initialDelaySeconds: 30
      periodSeconds: 10

    readinessProbe:
      httpGet:
        path: /healthz
        port: 5678
      initialDelaySeconds: 5
      periodSeconds: 5

    nodeSelector: {}
    tolerations: []
    affinity: {}

  # N8N Worker instances
  worker:
    replicaCount: 1
    image:
      repository: "n8nio/n8n"
      tag: "latest"
      pullPolicy: IfNotPresent

    resources:
      limits:
        cpu: 250m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi

    livenessProbe: {}
    readinessProbe: {}
    nodeSelector: {}
    tolerations: []
    affinity: {}

  # N8N Service
  service:
    type: ClusterIP
    port: 5678

  # N8N Ingress configuration
  ingress:
    admin:
      enabled: false
      className: "nginx"
      annotations: {}
        # cert-manager.io/cluster-issuer: letsencrypt-prod
        # nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8"
      hosts:
        - host: n8n-admin.example.com
          paths:
            - path: /
              pathType: Prefix
      tls: []
        # - secretName: n8n-admin-tls
        #   hosts:
        #     - n8n-admin.example.com

    webhook:
      enabled: false
      className: "nginx"
      annotations: {}
        # cert-manager.io/cluster-issuer: letsencrypt-prod
      hosts:
        - host: n8n-webhook.example.com
          paths:
            - path: /webhook/
              pathType: Prefix
      tls: []
        # - secretName: n8n-webhook-tls
        #   hosts:
        #     - n8n-webhook.example.com

# Redis Configuration
redis:
  replicaCount: 1

  image:
    repository: "redis"
    tag: "6.2"
    pullPolicy: IfNotPresent

  # Redis authentication
  auth:
    enabled: false

  resources: {}
    # limits:
    #   cpu: 100m
    #   memory: 128Mi
    # requests:
    #   cpu: 50m
    #   memory: 64Mi

  livenessProbe:
    tcpSocket:
      port: 6379
    initialDelaySeconds: 30
    periodSeconds: 10

  readinessProbe:
    tcpSocket:
      port: 6379
    initialDelaySeconds: 5
    periodSeconds: 5

  service:
    type: ClusterIP
    port: 6379

  nodeSelector: {}
  tolerations: []
  affinity: {}

# PostgreSQL Configuration
postgresql:
  replicaCount: 1

  image:
    repository: "docker.io/bitnami/postgresql"
    tag: "17.5.0-debian-12-r12"
    pullPolicy: IfNotPresent

  debug: false

  service:
    type: ClusterIP
    port: 5432

  serviceAccount:
    create: true

  resources:
    limits:
      cpu: 150m
      ephemeral-storage: 2Gi
      memory: 192Mi
    requests:
      cpu: 100m
      ephemeral-storage: 50Mi
      memory: 128Mi

  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
    privileged: false
    readOnlyRootFilesystem: true
    runAsGroup: 1001
    runAsNonRoot: true
    runAsUser: 1001
    seccompProfile:
      type: RuntimeDefault

  podSecurityContext:
    fsGroup: 1001
    fsGroupChangePolicy: Always

  livenessProbe:
    exec:
      command:
        - /bin/sh
        - -c
        - exec pg_isready -U "n8n" -d "dbname=n8n" -h 127.0.0.1 -p 5432
    failureThreshold: 6
    initialDelaySeconds: 30
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 5

  readinessProbe:
    exec:
      command:
        - /bin/sh
        - -c
        - -e
        - |
          exec pg_isready -U "n8n" -d "dbname=n8n" -h 127.0.0.1 -p 5432
          [ -f /opt/bitnami/postgresql/tmp/.initialized ] || [ -f /bitnami/postgresql/.initialized ]
    failureThreshold: 6
    initialDelaySeconds: 5
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 5

  persistence:
    enabled: true
    size: 8Gi
    storageClass: ""
    accessModes:
      - ReadWriteOnce

  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - podAffinityTerm:
            labelSelector:
              matchLabels:
                app.kubernetes.io/component: postgresql
            topologyKey: kubernetes.io/hostname
          weight: 1

  nodeSelector: {}
  tolerations: []

# PgAdmin Configuration
pgadmin:
  enabled: true
  replicaCount: 1

  image:
    repository: "dpage/pgadmin4"
    tag: "8.5"
    pullPolicy: IfNotPresent

  resources: {}
    # limits:
    #   cpu: 200m
    #   memory: 256Mi
    # requests:
    #   cpu: 100m
    #   memory: 128Mi

  livenessProbe:
    httpGet:
      path: /misc/ping
      port: 80
    initialDelaySeconds: 30
    periodSeconds: 10

  readinessProbe:
    httpGet:
      path: /misc/ping
      port: 80
    initialDelaySeconds: 5
    periodSeconds: 5

  service:
    type: ClusterIP
    port: 80

  persistence:
    enabled: false
    size: 1Gi
    storageClass: ""
    accessModes:
      - ReadWriteOnce

  ingress:
    enabled: false
    className: "nginx"
    annotations: {}
      # cert-manager.io/cluster-issuer: letsencrypt-prod
      # nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8"
    hosts:
      - host: pgadmin.example.com
        paths:
          - path: /
            pathType: Prefix
    tls: []
      # - secretName: pgadmin-tls
      #   hosts:
      #     - pgadmin.example.com

  nodeSelector: {}
  tolerations: []
  affinity: {}
