﻿apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
  labels:
    k8s-app: kube-dns-autoscaler
  name: kube-dns-autoscaler
  namespace: kube-system
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      k8s-app: kube-dns-autoscaler
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        version_hash: "353189128"
      creationTimestamp: null
      labels:
        k8s-app: kube-dns-autoscaler
    spec:
      containers:
      - command:
        - /cluster-proportional-autoscaler
        - --namespace=kube-system
        - --configmap=kube-dns-autoscaler
        - --target=deployment/coredns
        - --logtostderr=true
        - --v=2
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-cluster-proportional-autoscaler-amd64@sha256:edf02609a4444da49c7f80bd1866169fa9867343cc803e802251a8bb6837f9fb
        imagePullPolicy: IfNotPresent
        name: autoscaler
        resources:
          requests:
            cpu: 20m
            memory: 10Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      serviceAccount: dns-autoscaler
      serviceAccountName: dns-autoscaler
      terminationGracePeriodSeconds: 30
      tolerations:
      - key: oci.oraclecloud.com/oke-is-preemptible
        operator: Exists
