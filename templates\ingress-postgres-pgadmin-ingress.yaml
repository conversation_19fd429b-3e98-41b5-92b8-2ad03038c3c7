﻿apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare
    kubernetes.io/timestamp: 06/30/2025 11:16:52
    nginx.ingress.kubernetes.io/whitelist-source-range: *********/24,*********/24
  name: pgadmin-ingress
  namespace: postgres
spec:
  ingressClassName: nginx
  rules:
  - host: pgadmin.locke.cz
    http:
      paths:
      - backend:
          service:
            name: pgadmin-service
            port:
              number: 80
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - pgadmin.locke.cz
    secretName: pgadmin-tls
