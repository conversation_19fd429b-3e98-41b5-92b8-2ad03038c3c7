﻿{{- if and .Values.pgadmin.enabled .Values.pgadmin.ingress.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "mychart.fullname" . }}-pgadmin
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: pgadmin
  {{- with .Values.pgadmin.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.pgadmin.ingress.className }}
  ingressClassName: {{ .Values.pgadmin.ingress.className }}
  {{- end }}
  {{- if .Values.pgadmin.ingress.tls }}
  tls:
    {{- range .Values.pgadmin.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.pgadmin.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if .pathType }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              service:
                name: {{ include "mychart.fullname" $ }}-pgadmin
                port:
                  number: {{ $.Values.pgadmin.service.port }}
          {{- end }}
    {{- end }}
{{- end }}
