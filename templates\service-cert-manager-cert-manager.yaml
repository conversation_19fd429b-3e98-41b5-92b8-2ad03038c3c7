﻿apiVersion: v1
kind: Service
metadata:
  annotations:
    meta.helm.sh/release-name: cert-manager
    meta.helm.sh/release-namespace: cert-manager
  labels:
    app: cert-manager
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.18.1
    helm.sh/chart: cert-manager-v1.18.1
  name: cert-manager
  namespace: cert-manager
spec:
  clusterIP: ************
  clusterIPs:
  - ************
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: tcp-prometheus-servicemonitor
    port: 9402
    targetPort: http-metrics
  selector:
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: cert-manager
