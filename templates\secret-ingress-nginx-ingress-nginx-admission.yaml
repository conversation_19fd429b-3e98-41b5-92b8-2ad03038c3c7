﻿apiVersion: v1
data:
  ca: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJkVENDQVJ1Z0F3SUJBZ0lRUDladGdRMUljRmVxd1RCczV0emIxVEFLQmdncWhrak9QUVFEQWpBUE1RMHcKQ3dZRFZRUUtFd1J1YVd3eE1DQVhEVEkxTURZek1EQTNNREExTjFvWUR6SXhNalV3TmpBMk1EY3dNRFUzV2pBUApNUTB3Q3dZRFZRUUtFd1J1YVd3eE1Ga3dFd1lIS29aSXpqMENBUVlJS29aSXpqMERBUWNEUWdBRTVSSVkraFFnCnlYNlRwTVlCcWo3MnY4OUM0VTNtRmJoUWZIMEJXZkxtVm5qSUdoN2RuamEwTmJTYnNRck5mbzIyWGg0MVkwNkEKWUJBdlZLdjZRaEVKNnFOWE1GVXdEZ1lEVlIwUEFRSC9CQVFEQWdJRU1CTUdBMVVkSlFRTU1Bb0dDQ3NHQVFVRgpCd01CTUE4R0ExVWRFd0VCL3dRRk1BTUJBZjh3SFFZRFZSME9CQllFRkVFc2pFQnBST05rMzdndkdLWGFVRndzCkNWVHBNQW9HQ0NxR1NNNDlCQU1DQTBnQU1FVUNJUURPaGxvOHROYmswelZoOWRpK3pTNEp1L0tVbmJHZm9aVVoKN0xob1NiNm43UUlnU3kyV1RaSUJHZDlabkJ4M2NwRksyb1dRdVRVd3Y5aU1LMDI4UXZmcEFUaz0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
  cert: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJ1ekNDQVdHZ0F3SUJBZ0lSQUlzWTFMZUUwWloxR0hwbEFhbkFpUnN3Q2dZSUtvWkl6ajBFQXdJd0R6RU4KTUFzR0ExVUVDaE1FYm1sc01UQWdGdzB5TlRBMk16QXdOekF3TlRkYUdBOHlNVEkxTURZd05qQTNNREExTjFvdwpEekVOTUFzR0ExVUVDaE1FYm1sc01qQlpNQk1HQnlxR1NNNDlBZ0VHQ0NxR1NNNDlBd0VIQTBJQUJDMUJxN0RiClJCRmU0SnVVQUFhUmF4b3ZVWmhUOXVaVDJLTkhzbDQvaGpiRlFHTTlGMlFWS3Vpa1BybFAvdklCSWFiOW5YeGwKS2lNZFhkMXBsckZiSjlpamdac3dnWmd3RGdZRFZSMFBBUUgvQkFRREFnV2dNQk1HQTFVZEpRUU1NQW9HQ0NzRwpBUVVGQndNQk1Bd0dBMVVkRXdFQi93UUNNQUF3WXdZRFZSMFJCRnd3V29JaWFXNW5jbVZ6Y3kxdVoybHVlQzFqCmIyNTBjbTlzYkdWeUxXRmtiV2x6YzJsdmJvSTBhVzVuY21WemN5MXVaMmx1ZUMxamIyNTBjbTlzYkdWeUxXRmsKYldsemMybHZiaTVwYm1keVpYTnpMVzVuYVc1NExuTjJZekFLQmdncWhrak9QUVFEQWdOSUFEQkZBaUI3Q0IzeQpZb0tLYlhTNnI2aUFGUndUcGdGTDBJcUlwRnlxWHZCNkRQOGVQUUloQUt1M2pmRVI5SFZ2Q2JzMU5hUWF1V21lCnRIZGlwMkZzcTgrV1EwMGN5eVdWCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  key: ****************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  name: ingress-nginx-admission
  namespace: ingress-nginx
type: Opaque
