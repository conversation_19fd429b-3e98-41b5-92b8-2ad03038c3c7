# N8N Stack Helm Chart

A complete N8N workflow automation platform with PostgreSQL database, Redis queue, and PgAdmin management interface.

## Description

This Helm chart deploys a full-featured N8N automation platform including:

- **N8N Main**: The primary N8N instance for workflow editing and management
- **N8N Worker**: Scalable worker instances for executing workflows
- **PostgreSQL**: Database backend for N8N data persistence
- **Redis**: Queue system for N8N worker communication
- **PgAdmin**: Web-based PostgreSQL administration interface (optional)

## Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+
- PV provisioner support in the underlying infrastructure (for PostgreSQL persistence)

## Installation

### Quick Start

1. **Clone or download this chart**

2. **Set required secrets** (see Configuration section below)

3. **Install the chart:**

```bash
helm install my-n8n ./n8n-stack \
  --set secrets.n8nDbPassword="your-db-password" \
  --set secrets.n8nAuthPassword="your-admin-password" \
  --set secrets.postgresqlPassword="your-postgres-password" \
  --set secrets.postgresqlRootPassword="your-postgres-root-password" \
  --set secrets.pgadminEmail="<EMAIL>" \
  --set secrets.pgadminPassword="your-pgadmin-password" \
  --set n8n.editorBaseUrl="https://n8n-admin.yourdomain.com" \
  --set n8n.webhookUrl="https://n8n-webhook.yourdomain.com"
```

### Production Installation

For production deployments, create a values file:

```bash
# Create custom values file
cat > my-values.yaml << EOF
secrets:
  n8nDbPassword: "secure-db-password"
  n8nAuthPassword: "secure-admin-password"
  postgresqlPassword: "secure-postgres-password"
  postgresqlRootPassword: "secure-postgres-root-password"
  pgadminEmail: "<EMAIL>"
  pgadminPassword: "secure-pgadmin-password"

n8n:
  editorBaseUrl: "https://n8n-admin.yourdomain.com"
  webhookUrl: "https://n8n-webhook.yourdomain.com"
  
  ingress:
    admin:
      enabled: true
      className: "nginx"
      annotations:
        cert-manager.io/cluster-issuer: letsencrypt-prod
        nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8"
      hosts:
        - host: n8n-admin.yourdomain.com
          paths:
            - path: /
              pathType: Prefix
      tls:
        - secretName: n8n-admin-tls
          hosts:
            - n8n-admin.yourdomain.com
    
    webhook:
      enabled: true
      className: "nginx"
      annotations:
        cert-manager.io/cluster-issuer: letsencrypt-prod
      hosts:
        - host: n8n-webhook.yourdomain.com
          paths:
            - path: /webhook/
              pathType: Prefix
      tls:
        - secretName: n8n-webhook-tls
          hosts:
            - n8n-webhook.yourdomain.com

pgadmin:
  enabled: true
  ingress:
    enabled: true
    className: "nginx"
    annotations:
      cert-manager.io/cluster-issuer: letsencrypt-prod
      nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8"
    hosts:
      - host: pgadmin.yourdomain.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: pgadmin-tls
        hosts:
          - pgadmin.yourdomain.com

postgresql:
  persistence:
    enabled: true
    size: 20Gi
    storageClass: "fast-ssd"
EOF

# Install with custom values
helm install my-n8n ./n8n-stack -f my-values.yaml
```

## Configuration

### Required Configuration

The following values **MUST** be configured before installation:

| Parameter | Description | Example |
|-----------|-------------|---------|
| `secrets.n8nDbPassword` | Password for N8N database user | `"secure-password"` |
| `secrets.n8nAuthPassword` | Password for N8N admin user | `"admin-password"` |
| `secrets.postgresqlPassword` | Password for PostgreSQL N8N user | `"postgres-password"` |
| `secrets.postgresqlRootPassword` | Password for PostgreSQL root user | `"root-password"` |
| `secrets.pgadminEmail` | Email for PgAdmin login | `"<EMAIL>"` |
| `secrets.pgadminPassword` | Password for PgAdmin login | `"pgadmin-password"` |
| `n8n.editorBaseUrl` | URL for N8N admin interface | `"https://n8n-admin.example.com"` |
| `n8n.webhookUrl` | URL for N8N webhooks | `"https://n8n-webhook.example.com"` |

### Key Configuration Options

#### N8N Configuration

```yaml
n8n:
  main:
    replicaCount: 1
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi
  
  worker:
    replicaCount: 2  # Scale workers as needed
    resources:
      limits:
        cpu: 250m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi
```

#### PostgreSQL Configuration

```yaml
postgresql:
  persistence:
    enabled: true
    size: 20Gi
    storageClass: "fast-ssd"
  
  resources:
    limits:
      cpu: 500m
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 512Mi
```

#### Redis Configuration

```yaml
redis:
  auth:
    enabled: true  # Enable Redis authentication
  
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi
```

#### PgAdmin Configuration

```yaml
pgadmin:
  enabled: true
  persistence:
    enabled: true
    size: 1Gi
```

## Accessing the Application

After installation, you can access:

1. **N8N Admin Interface**: `https://n8n-admin.yourdomain.com`
   - Username: `admin` (configurable via `n8n.auth.user`)
   - Password: Value of `secrets.n8nAuthPassword`

2. **PgAdmin Interface**: `https://pgadmin.yourdomain.com` (if enabled)
   - Email: Value of `secrets.pgadminEmail`
   - Password: Value of `secrets.pgadminPassword`

## Upgrading

```bash
helm upgrade my-n8n ./n8n-stack -f my-values.yaml
```

## Uninstalling

```bash
helm uninstall my-n8n
```

**Note**: This will not delete PersistentVolumeClaims. Delete them manually if needed:

```bash
kubectl delete pvc -l app.kubernetes.io/instance=my-n8n
```

## Troubleshooting

### Common Issues

1. **Pods stuck in Pending state**
   - Check if PersistentVolumes can be provisioned
   - Verify storage class exists: `kubectl get storageclass`

2. **N8N can't connect to database**
   - Verify PostgreSQL is running: `kubectl get pods -l app.kubernetes.io/component=postgresql`
   - Check database credentials in secrets

3. **Ingress not working**
   - Verify ingress controller is installed
   - Check DNS resolution for configured hostnames
   - Verify TLS certificates if using HTTPS

### Debugging Commands

```bash
# Check all pods
kubectl get pods -l app.kubernetes.io/instance=my-n8n

# Check logs
kubectl logs -l app.kubernetes.io/component=n8n-main
kubectl logs -l app.kubernetes.io/component=postgresql

# Check services
kubectl get svc -l app.kubernetes.io/instance=my-n8n

# Check ingress
kubectl get ingress -l app.kubernetes.io/instance=my-n8n
```

## Support

For issues related to:
- **N8N**: Visit [N8N Documentation](https://docs.n8n.io/)
- **PostgreSQL**: Visit [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- **This Helm Chart**: Create an issue in the repository

## License

This chart is provided as-is under the MIT License.
