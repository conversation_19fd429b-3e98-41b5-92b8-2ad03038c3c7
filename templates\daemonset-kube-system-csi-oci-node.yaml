﻿apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    deprecated.daemonset.template.generation: "2"
  name: csi-oci-node
  namespace: kube-system
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: csi-oci-node
  template:
    metadata:
      annotations:
        checksum/config: 6bc5cc92b481f982
      creationTimestamp: null
      labels:
        app: csi-oci-node
        role: csi-oci
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-role.kubernetes.io/virtual-node
                operator: DoesNotExist
      containers:
      - args:
        - --v=2
        - --csi-address=/csi/csi.sock
        - --kubelet-registration-path=/var/lib/kubelet/plugins/blockvolume.csi.oraclecloud.com/csi.sock
        - --endpoint=unix:///csi/csi.sock
        - --nodeid=$(KUBE_NODE_NAME)
        - --fss-endpoint=unix:///fss/csi.sock
        - --fss-csi-address=/fss/csi.sock
        - --fss-kubelet-registration-path=/var/lib/kubelet/plugins/fss.csi.oraclecloud.com/csi.sock
        - --fss-csi-driver-enabled=true
        - --lustre-endpoint=unix:///lustre/csi.sock
        - --lustre-csi-address=/lustre/csi.sock
        - --lustre-kubelet-registration-path=/var/lib/kubelet/plugins/lustre.csi.oraclecloud.com/csi.sock
        command:
        - /usr/local/bin/oci-csi-node-driver
        env:
        - name: KUBE_NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: PATH
          value: /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/host/usr/bin:/host/sbin
        - name: LUSTRE_DRIVER_ENABLED
          value: "true"
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-cloud-provider-oci:v1.33-7044c2f4a32-10@sha256:51fb1bdf033ff5416030a73155a14475dbfdf41873ee3419bc3aa73ba61b0b13
        imagePullPolicy: Always
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - rm -rf /registration/blockvolume.csi.oraclecloud.com /registration/blockvolume.csi.oraclecloud.com-reg.sock
                /registration/fss.csi.oraclecloud.com /registration/fss.csi.oraclecloud.com-reg.sock
                /registration/lustre.csi.oraclecloud.com /registration/lustre.csi.oraclecloud.com-reg.sock
        name: csi-node-driver
        resources:
          limits:
            cpu: 500m
            memory: 300Mi
          requests:
            cpu: 30m
            memory: 70Mi
        securityContext:
          allowPrivilegeEscalation: true
          privileged: true
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /csi
          name: plugin-dir
        - mountPath: /var/lib/kubelet
          mountPropagation: Bidirectional
          name: pods-mount-dir
        - mountPath: /dev
          name: device-dir
        - mountPath: /registration
          name: registration-dir
        - mountPath: /host
          mountPropagation: HostToContainer
          name: host-root
        - mountPath: /sbin/iscsiadm
          name: chroot-iscsiadm
          subPath: iscsiadm
        - mountPath: /fss
          name: fss-plugin-dir
        - mountPath: /host/var/lib/kubelet
          mountPropagation: Bidirectional
          name: encrypt-pods-mount-dir
        - mountPath: /sbin/umount.oci-fss
          name: fss-driver-mounts
          subPath: umount.oci-fss
        - mountPath: /sbin/umount
          name: fss-driver-mounts
          subPath: umount
        - mountPath: /sbin/mount
          name: fss-driver-mounts
          subPath: mount
        - mountPath: /lustre
          name: lustre-plugin-dir
      dnsPolicy: ClusterFirst
      hostNetwork: true
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        runAsNonRoot: false
        runAsUser: 0
      serviceAccount: csi-oci-node-sa
      serviceAccountName: csi-oci-node-sa
      terminationGracePeriodSeconds: 30
      tolerations:
      - key: CriticalAddonsOnly
        operator: Exists
      - operator: Exists
      volumes:
      - hostPath:
          path: /var/lib/kubelet/plugins_registry/
          type: DirectoryOrCreate
        name: registration-dir
      - hostPath:
          path: /var/lib/kubelet/plugins/blockvolume.csi.oraclecloud.com
          type: DirectoryOrCreate
        name: plugin-dir
      - hostPath:
          path: /var/lib/kubelet
          type: Directory
        name: pods-mount-dir
      - hostPath:
          path: /dev
          type: ""
        name: device-dir
      - hostPath:
          path: /
          type: Directory
        name: host-root
      - configMap:
          defaultMode: 493
          items:
          - key: iscsiadm
            path: iscsiadm
          name: oci-csi-iscsiadm
        name: chroot-iscsiadm
      - hostPath:
          path: /var/lib/kubelet/plugins/fss.csi.oraclecloud.com
          type: DirectoryOrCreate
        name: fss-plugin-dir
      - hostPath:
          path: /var/lib/kubelet
          type: Directory
        name: encrypt-pods-mount-dir
      - configMap:
          defaultMode: 493
          name: oci-fss-csi
        name: fss-driver-mounts
      - hostPath:
          path: /var/lib/kubelet/plugins/lustre.csi.oraclecloud.com
          type: DirectoryOrCreate
        name: lustre-plugin-dir
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
