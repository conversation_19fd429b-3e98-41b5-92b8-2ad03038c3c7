﻿apiVersion: v1
data:
  tls.crt: 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
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/alt-names: pgadmin.locke.cz
    cert-manager.io/certificate-name: pgadmin-tls
    cert-manager.io/common-name: pgadmin.locke.cz
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: cert-manager.io
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: letsencrypt-cloudflare
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
  name: pgadmin-tls
  namespace: postgres
type: kubernetes.io/tls
