﻿apiVersion: v1
kind: Service
metadata:
  name: {{ include "mychart.fullname" . }}-redis
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: redis
spec:
  type: {{ .Values.redis.service.type }}
  ports:
  - port: {{ .Values.redis.service.port }}
    targetPort: redis
    protocol: TCP
    name: redis
  selector:
    {{- include "mychart.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: redis
