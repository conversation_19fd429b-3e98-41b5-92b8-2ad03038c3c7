﻿apiVersion: v1
kind: Service
metadata:
  annotations:
    meta.helm.sh/release-name: cert-manager
    meta.helm.sh/release-namespace: cert-manager
  labels:
    app: webhook
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: webhook
    app.kubernetes.io/version: v1.18.1
    helm.sh/chart: cert-manager-v1.18.1
  name: cert-manager-webhook
  namespace: cert-manager
spec:
  clusterIP: *************
  clusterIPs:
  - *************
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: https
    port: 443
    targetPort: https
  - name: metrics
    port: 9402
    targetPort: http-metrics
  selector:
    app.kubernetes.io/component: webhook
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/name: webhook
