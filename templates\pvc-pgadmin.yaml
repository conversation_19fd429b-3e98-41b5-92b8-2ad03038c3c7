{{- if and .Values.pgadmin.enabled .Values.pgadmin.persistence.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "mychart.fullname" . }}-pgadmin-pvc
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: pgadmin
spec:
  accessModes:
    {{- range .Values.pgadmin.persistence.accessModes }}
    - {{ . | quote }}
    {{- end }}
  resources:
    requests:
      storage: {{ .Values.pgadmin.persistence.size | quote }}
  {{- if .Values.pgadmin.persistence.storageClass }}
  {{- if (eq "-" .Values.pgadmin.persistence.storageClass) }}
  storageClassName: ""
  {{- else }}
  storageClassName: {{ .Values.pgadmin.persistence.storageClass | quote }}
  {{- end }}
  {{- end }}
{{- end }}
