﻿apiVersion: v1
data:
  tls.crt: 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
  tls.key: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/alt-names: n8n-admin.locke.cz
    cert-manager.io/certificate-name: n8n-admin-tls
    cert-manager.io/common-name: n8n-admin.locke.cz
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: cert-manager.io
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: letsencrypt-cloudflare
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
  name: n8n-admin-tls
  namespace: n8n
type: kubernetes.io/tls
