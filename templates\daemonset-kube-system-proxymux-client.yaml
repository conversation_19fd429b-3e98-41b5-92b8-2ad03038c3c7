﻿apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    deprecated.daemonset.template.generation: "1"
    scheduler.alpha.kubernetes.io/critical-pod: ""
  labels:
    oke-app: proxymux-client-ds
  name: proxymux-client
  namespace: kube-system
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      oke-app: proxymux-client-ds
  template:
    metadata:
      creationTimestamp: null
      labels:
        oke-app: proxymux-client-ds
    spec:
      containers:
      - args:
        - --config=/mnt/etc/proxymux/config.yaml
        - --verbosity=info
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-proxymux-cli:8d4509c1e518fcb4e1a95191a4b6dff29a283dee-115@sha256:866d637f98a0e5451816d6992f90571e95d638d49636dbb7d9eaee873fb122f0
        imagePullPolicy: IfNotPresent
        name: proxymux-client
        resources:
          limits:
            cpu: 500m
            memory: 256Mi
          requests:
            cpu: 50m
            memory: 64Mi
        securityContext:
          allowPrivilegeEscalation: true
          privileged: true
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /mnt/etc/proxymux/
          name: proxymux-cfg
        - mountPath: /etc/kubernetes/
          name: kubernetes-cfg
        - mountPath: /run/systemd/
          name: systemd
        - mountPath: /var/run/dbus/
          name: dbus
        - mountPath: /etc/pki/
          name: pki
        - mountPath: /etc/kubernetes/kube-root-ca/
          name: kube-root-ca
        - mountPath: /etc/kubernetes/kube-certificate-rotation/
          name: kube-certificate-rotation
        - mountPath: /var/lib/kubelet/pki
          name: var-lib-kubelet-pki
      dnsPolicy: ClusterFirst
      hostNetwork: true
      nodeSelector:
        node.info.ds_proxymux_client: "true"
      priority: **********
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        runAsNonRoot: false
        runAsUser: 0
      serviceAccount: proxymux-client
      serviceAccountName: proxymux-client
      terminationGracePeriodSeconds: 30
      tolerations:
      - key: CriticalAddonsOnly
        operator: Exists
      - operator: Exists
      volumes:
      - hostPath:
          path: /etc/proxymux/
          type: ""
        name: proxymux-cfg
      - hostPath:
          path: /etc/kubernetes/
          type: ""
        name: kubernetes-cfg
      - hostPath:
          path: /run/systemd/
          type: ""
        name: systemd
      - hostPath:
          path: /var/run/dbus/
          type: ""
        name: dbus
      - hostPath:
          path: /etc/pki/
          type: ""
        name: pki
      - configMap:
          defaultMode: 420
          name: kube-root-ca.crt
          optional: true
        name: kube-root-ca
      - configMap:
          defaultMode: 420
          name: kube-certificate-rotation
          optional: true
        name: kube-certificate-rotation
      - hostPath:
          path: /var/lib/kubelet/pki/
          type: ""
        name: var-lib-kubelet-pki
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
