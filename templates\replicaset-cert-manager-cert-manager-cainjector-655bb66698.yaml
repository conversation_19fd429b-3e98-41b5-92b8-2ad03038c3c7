﻿apiVersion: apps/v1
kind: ReplicaSet
metadata:
  annotations:
    deployment.kubernetes.io/desired-replicas: "1"
    deployment.kubernetes.io/max-replicas: "2"
    deployment.kubernetes.io/revision: "1"
    meta.helm.sh/release-name: cert-manager
    meta.helm.sh/release-namespace: cert-manager
  labels:
    app: cainjector
    app.kubernetes.io/component: cainjector
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: cainjector
    app.kubernetes.io/version: v1.18.1
    helm.sh/chart: cert-manager-v1.18.1
    pod-template-hash: 655bb66698
  name: cert-manager-cainjector-655bb66698
  namespace: cert-manager
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: cainjector
      app.kubernetes.io/instance: cert-manager
      app.kubernetes.io/name: cainjector
      pod-template-hash: 655bb66698
  template:
    metadata:
      annotations:
        prometheus.io/path: /metrics
        prometheus.io/port: "9402"
        prometheus.io/scrape: "true"
      creationTimestamp: null
      labels:
        app: cainjector
        app.kubernetes.io/component: cainjector
        app.kubernetes.io/instance: cert-manager
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: cainjector
        app.kubernetes.io/version: v1.18.1
        helm.sh/chart: cert-manager-v1.18.1
        pod-template-hash: 655bb66698
    spec:
      containers:
      - args:
        - --v=2
        - --leader-election-namespace=kube-system
        env:
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        image: quay.io/jetstack/cert-manager-cainjector:v1.18.1
        imagePullPolicy: IfNotPresent
        name: cert-manager-cainjector
        ports:
        - containerPort: 9402
          name: http-metrics
          protocol: TCP
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      enableServiceLinks: false
      nodeSelector:
        kubernetes.io/os: linux
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      serviceAccount: cert-manager-cainjector
      serviceAccountName: cert-manager-cainjector
      terminationGracePeriodSeconds: 30
