﻿apiVersion: v1
data:
  ca.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJ3akNDQVVpZ0F3SUJBZ0lSQUtsRUZ5bEkxRlRuTWdMaXY2ZytrTEl3Q2dZSUtvWkl6ajBFQXdNd0lqRWcKTUI0R0ExVUVBeE1YWTJWeWRDMXRZVzVoWjJWeUxYZGxZbWh2YjJzdFkyRXdIaGNOTWpVd05qTXdNRGN6T1RReApXaGNOTWpZd05qTXdNRGN6T1RReFdqQWlNU0F3SGdZRFZRUURFeGRqWlhKMExXMWhibUZuWlhJdGQyVmlhRzl2CmF5MWpZVEIyTUJBR0J5cUdTTTQ5QWdFR0JTdUJCQUFpQTJJQUJON0NxeUZzUzkwam0rTE5aRGFMS3dFRUNkb0cKUVhNOURqY1RCUTdFSG84U0xiUkE5WnhkZWJUT1B3Vi9OeTBwTjVyT2FTU0hHKzc2QVFxTWQ4M0pxelpIcU9FbgpseVJ5ZmlnT0NONEVTRHVMcGdwbTZiZ3JHbkV5cjBKYWxkSW1lS05DTUVBd0RnWURWUjBQQVFIL0JBUURBZ0trCk1BOEdBMVVkRXdFQi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZNQnp0RzUwdUc2c2doaWx3QkNBMXF3emZtZkkKTUFvR0NDcUdTTTQ5QkFNREEyZ0FNR1VDTUYzTWtJcUx3NUU0OS9RRi8xWXE2d3NqVndmL0h1TnJmZU1zYUdhSgpneGd5dzVud0JzWURqWVdsMzBUZ0dzaXdzUUl4QVA5OFltWEduZjRuNmFLTGJtbFlRaWsvdUFZYWJYRmx2d0Z6ClJPNlBQUzdORCsxaUJlSEZwOGNwTDhJMUxQWHNoZz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJ3akNDQVVpZ0F3SUJBZ0lSQUtsRUZ5bEkxRlRuTWdMaXY2ZytrTEl3Q2dZSUtvWkl6ajBFQXdNd0lqRWcKTUI0R0ExVUVBeE1YWTJWeWRDMXRZVzVoWjJWeUxYZGxZbWh2YjJzdFkyRXdIaGNOTWpVd05qTXdNRGN6T1RReApXaGNOTWpZd05qTXdNRGN6T1RReFdqQWlNU0F3SGdZRFZRUURFeGRqWlhKMExXMWhibUZuWlhJdGQyVmlhRzl2CmF5MWpZVEIyTUJBR0J5cUdTTTQ5QWdFR0JTdUJCQUFpQTJJQUJON0NxeUZzUzkwam0rTE5aRGFMS3dFRUNkb0cKUVhNOURqY1RCUTdFSG84U0xiUkE5WnhkZWJUT1B3Vi9OeTBwTjVyT2FTU0hHKzc2QVFxTWQ4M0pxelpIcU9FbgpseVJ5ZmlnT0NONEVTRHVMcGdwbTZiZ3JHbkV5cjBKYWxkSW1lS05DTUVBd0RnWURWUjBQQVFIL0JBUURBZ0trCk1BOEdBMVVkRXdFQi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZNQnp0RzUwdUc2c2doaWx3QkNBMXF3emZtZkkKTUFvR0NDcUdTTTQ5QkFNREEyZ0FNR1VDTUYzTWtJcUx3NUU0OS9RRi8xWXE2d3NqVndmL0h1TnJmZU1zYUdhSgpneGd5dzVud0JzWURqWVdsMzBUZ0dzaXdzUUl4QVA5OFltWEduZjRuNmFLTGJtbFlRaWsvdUFZYWJYRmx2d0Z6ClJPNlBQUzdORCsxaUJlSEZwOGNwTDhJMUxQWHNoZz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.key: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/allow-direct-injection: "true"
  labels:
    app.kubernetes.io/managed-by: cert-manager-webhook
  name: cert-manager-webhook-ca
  namespace: cert-manager
type: Opaque
