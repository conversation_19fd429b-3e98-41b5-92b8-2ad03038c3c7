﻿apiVersion: v1
data:
  config.conf: '{"apiVersion":"kubeproxy.config.k8s.io/v1alpha1","kind":"KubeProxyConfiguration","bindAddress":"0.0.0.0","healthzBindAddress":"0.0.0.0:10256","metricsBindAddress":"0.0.0.0:10249","clientConnection":{"acceptContentTypes":"","burst":10,"contentType":"application/vnd.kubernetes.protobuf","kubeconfig":"/var/lib/kube-proxy/kubeconfig.conf","qps":5},"detectLocalMode":"InterfaceNamePrefix","detectLocal":{"interfaceNamePrefix":"oci"},"configSyncPeriod":"15m0s","conntrack":{"maxPerCore":32768,"min":131072,"tcpCloseWaitTimeout":"1h0m0s","tcpEstablishedTimeout":"24h0m0s"},"enableProfiling":false,"hostnameOverride":"","iptables":{"masqueradeAll":false,"masqueradeBit":14,"minSyncPeriod":"0s","syncPeriod":"30s"},"ipvs":{"excludeCIDRs":null,"minSyncPeriod":"0s","scheduler":"","strictARP":false,"syncPeriod":"30s"},"mode":"","nodePortAddresses":["primary"],"oomScoreAdj":-999,"portRange":"","udpIdleTimeout":"4h10m0s","winkernel":{"enableDSR":false,"networkName":"","sourceVip":""}}'
  kube_proxy_init.sh: |-
    #!/bin/sh
    log_info () { echo "$(date) $@"; }
    log_error () { >&2 echo "$(date) $@"; }

    IMDS_IPV4="http://***************"
    IMDS_IPV6="http://[fd00:c1::a9fe:a9fe]"

    check_endpoint() {
        curl -g -H "Authorization: Bearer Oracle" --connect-timeout 3 -s -o /dev/null -w "%{http_code}" "$1"
    }

    if [ "$(check_endpoint $IMDS_IPV4/opc/v2/instance/)" -eq 200 ]; then
        IMDS_ENDPOINT=$IMDS_IPV4
    elif [ "$(check_endpoint $IMDS_IPV6/opc/v2/instance/)" -eq 200 ]; then
        IMDS_ENDPOINT=$IMDS_IPV6
    else
        log_error "No IMDS endpoint reachable"
        exit 1
    fi

    log_info "Using IMDS endpoint: $IMDS_ENDPOINT"

    log_info "Configure kube-proxy mode(iptables or ipvs)"
    cp /var/lib/kube-proxy-config/kubeconfig.conf /var/lib/kube-proxy/kubeconfig.conf || exit $?
    cp /var/lib/kube-proxy-config/config.conf /var/lib/kube-proxy/config.conf || exit $?
    log_info "Copied kube-proxy configuration files"
    sed -i 's/\("\?kubeconfig"\?\s*:\s*\)"\?[^"]*"\?/\1"\/var\/lib\/kube-proxy\/kubeconfig\.conf"/' /var/lib/kube-proxy/config.conf || exit $?

    oke_node_version=$(curl -g -s -H "Authorization: Bearer Oracle" --max-time 30 --fail -L0 "$IMDS_ENDPOINT/opc/v2/instance/metadata/oke-k8version")
    version_fetch_result=$?
    log_info "K8s version($version_fetch_result): $oke_node_version"
    if [ $version_fetch_result -ne 0 ]; then
      oke_node_version=$(curl -g -s --max-time 30 --fail -L0 "$IMDS_ENDPOINT/opc/v1/instance/metadata/oke-k8version")
      version_fetch_result=$?
      log_info "K8s version($version_fetch_result): $oke_node_version"
      if [ $version_fetch_result -ne 0 ]; then
        k8s_version_path="/var/lib/kube-proxy-k8s-version"
        log_info "Fetching K8s version from ${k8s_version_path}"
        oke_node_version=$(cat "$k8s_version_path")
        if [ -z "$oke_node_version" ]; then
          log_error "Unable to fetch K8s version($version_fetch_result): $oke_node_version"
          exit 1
        fi
      fi
    fi

    oke_kubeproxy_proxy=$(curl -g -s -H "Authorization: Bearer Oracle" --max-time 30 --fail -L0 "$IMDS_ENDPOINT/opc/v2/instance/metadata/oke-kubeproxy-proxy-mode")
    kubeproxy_mode_fetch_result=$?
    log_info "kubeproxy mode ($kubeproxy_mode_fetch_result): $oke_kubeproxy_proxy"
    if [ $kubeproxy_mode_fetch_result -ne 0 ]; then
      oke_kubeproxy_proxy=$(curl -g -s --max-time 30 --fail -L0 "$IMDS_ENDPOINT/opc/v1/instance/metadata/oke-kubeproxy-proxy-mode")
      kubeproxy_mode_fetch_result=$?
      log_info "kubeproxy mode ($kubeproxy_mode_fetch_result): $oke_kubeproxy_proxy"
      if [ $kubeproxy_mode_fetch_result -ne 0 ]; then
        log_error "Unable to fetch kubeproxy mode from imds ($kubeproxy_mode_fetch_result): $oke_kubeproxy_proxy. Using iptables."
        oke_kubeproxy_proxy="iptables"
      fi
    fi

    major_version=$(echo "$oke_node_version" | sed 's/v\([0-9]*\).\([0-9]*\).*/\1/')
    minor_version=$(echo "$oke_node_version" | sed 's/v\([0-9]*\).\([0-9]*\).*/\2/')
    if [ "$oke_kubeproxy_proxy" = "ipvs" ] && ( ( [ "$major_version" -gt 1 ] ) || ( [ "$major_version" -eq 1 ] && [ "$minor_version" -gt 21 ] ) ); then
      sed -i 's/\("\?mode"\?\s*:\s*\)"[^"]*"/\1"ipvs"/' /var/lib/kube-proxy/config.conf || exit $?
      log_info "ipvs mode is enabled"
    else
      log_info "iptables mode is enabled"
    fi
    log_info "kube-proxy configuration is completed"
  kubeconfig.conf: '{"apiVersion":"v1","kind":"Config","clusters":[{"cluster":{"certificate-authority":"/var/run/secrets/kubernetes.io/serviceaccount/ca.crt","server":"https://********:6443"},"name":"default"}],"contexts":[{"context":{"cluster":"default","namespace":"default","user":"default"},"name":"default"}],"current-context":"default","users":[{"name":"default","user":{"tokenFile":"/var/run/secrets/kubernetes.io/serviceaccount/token"}}]}'
kind: ConfigMap
metadata:
  labels:
    app: kube-proxy
  name: kube-proxy
  namespace: kube-system
