﻿apiVersion: v1
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUZCekNDQSsrZ0F3SUJBZ0lTQlExTGNuL1pSTTZ1aGYvaTl1dSt6RHJ2TUEwR0NTcUdTSWIzRFFFQkN3VUEKTURNeEN6QUpCZ05WQkFZVEFsVlRNUll3RkFZRFZRUUtFdzFNWlhRbmN5QkZibU55ZVhCME1Rd3dDZ1lEVlFRRApFd05TTVRBd0hoY05NalV3TmpNd01EZ3hPVEE1V2hjTk1qVXdPVEk0TURneE9UQTRXakFmTVIwd0d3WURWUVFECkV4UnVPRzR0ZDJWaWFHOXZheTVzYjJOclpTNWplakNDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0MKQVFvQ2dnRUJBS3pLQndzMWpEMGtLNWNjaHpYL0FNM0N5MUI0UnhNeG1Sd0x6a3JjY0g0QU1ReVA1K0MralloMAovN3pMeGlMbGNjNFhuWlBFVXdSTi9oWHlieUttUlNEYWx5TXplblpZTGExdk8vcXA2dzRTZ0FjdU40YVZFb2RHCkR5T1k4M2FFYkQzYUl3dTZjcFhkb1Z4Nzg1RC9rMGxvMWhlYnI5VFRmT1plcmlFYm9oSnVNRm4yRmVqUVgwTXEKd01OQ3pRVTRKS2ZXSTN1MnFYSzJaWitOeTdPRGNaZ0xsaVNYRHJ1TSszZTFwRkFYdUhvRG9BR2JtblArMUY4NApXMzl1cWptMU5MOGNZNHBBQWt1enEvc3R5R25uSUxnQUFkZmpab2ZzdEpUWHFtSTk2L0dWM0g4SFA0MkdneVJRCm1vVVBKMWdWS3oxSW5henBLb3RNZXZEK1lVM1RZQThDQXdFQUFhT0NBaWN3Z2dJak1BNEdBMVVkRHdFQi93UUUKQXdJRm9EQWRCZ05WSFNVRUZqQVVCZ2dyQmdFRkJRY0RBUVlJS3dZQkJRVUhBd0l3REFZRFZSMFRBUUgvQkFJdwpBREFkQmdOVkhRNEVGZ1FVNVM4b1U3SWQ1NThobUUzenBxemltb3RKUEY4d0h3WURWUjBqQkJnd0ZvQVV1N3pEClI2WGt2S25HdzZSeURCQ05valhoeU9nd013WUlLd1lCQlFVSEFRRUVKekFsTUNNR0NDc0dBUVVGQnpBQ2hoZG8KZEhSd09pOHZjakV3TG1rdWJHVnVZM0l1YjNKbkx6QWZCZ05WSFJFRUdEQVdnaFJ1T0c0dGQyVmlhRzl2YXk1cwpiMk5yWlM1amVqQVRCZ05WSFNBRUREQUtNQWdHQm1lQkRBRUNBVEF2QmdOVkhSOEVLREFtTUNTZ0lxQWdoaDVvCmRIUndPaTh2Y2pFd0xtTXViR1Z1WTNJdWIzSm5MekV4TWk1amNtd3dnZ0VHQmdvckJnRUVBZFo1QWdRQ0JJSDMKQklIMEFQSUFkd0RkM01vMGxkZmhGZ1hubFRMNng1LzRQUnhRMzlzQU9oUVNkZ29zckx2SUtnQUFBWmZBSUxpUgpBQUFFQXdCSU1FWUNJUUR3azVhY1FRY0xUS3F3bHlJUWFKUHB2aE1XelhxQXFkc1dFVVpQcnd5UWVnSWhBTUVYClZSclpaNGNNUW1NZWNWSGNvSnhmeC9rcWFKOHZFTWE0bUNmTW1ScW9BSGNBN1R4TDF1Z0d3cVNpQUZmYnl5VGkKT0FIZlVTL3R4SWJGY0E4ZzNiYytQK0FBQUFHWHdDREFHUUFBQkFNQVNEQkdBaUVBeTl3cDV3QUVvQmdHMW1wagpBdTA1and3NUc4bUVldTBkZHYyOXlhdnprQVFDSVFEVmhjK3czNmNZOEZhLy9xbjZaWXlEV3hTeXRBMit6UmI5ClZPeDRSRGdHN1RBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQW0weFpjOE5RaG5SRGl0WWFOOTNlWEE1WlczUjMKREw0UEVRRlBoSFlJdTM5U0ZMU3hiWWNYU21MMUdiQ1FVUEJ1N3daRGMxVjdweS8wT2ZmZStqaUkzM2pNNVA3QQpnbmxydm5oOFBTQlU2SEE4UVFhMmhybmRiTWVPWlJlK3Z4U3A4M1h4aUNxSlBLekRMUlorZm5vUklMYy9mUnBUCnNvVlFJdXhSY1JtYzcrTGphQzUxOExpNU50WjU1VllMYW8wN2xTQVNyY2hKZk5YcnM0cUNPWkNYNGpYcVZmNkUKT1VMWFptMkZCbU5pdklKY213OUwra3lBQURIVHMyS0hWZFMwWXlSb200SnAwaFFhUnJMOVg4c0paMUxUNlU4SApCRURSK1NoV3pxZS82d1FXdi9tWWRmSUIvWXFjaWpVVTJQOXlmSVRMZmRBTnVtZTdRVWpFSGlsNmZ3PT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQotLS0tLUJFR0lOIENFUlRJRklDQVRFLS0tLS0KTUlJRkJUQ0NBdTJnQXdJQkFnSVFTNmhTay9lYUw2SnpCa3VvQkkxMTBEQU5CZ2txaGtpRzl3MEJBUXNGQURCUApNUXN3Q1FZRFZRUUdFd0pWVXpFcE1DY0dBMVVFQ2hNZ1NXNTBaWEp1WlhRZ1UyVmpkWEpwZEhrZ1VtVnpaV0Z5ClkyZ2dSM0p2ZFhBeEZUQVRCZ05WQkFNVERFbFRVa2NnVW05dmRDQllNVEFlRncweU5EQXpNVE13TURBd01EQmEKRncweU56QXpNVEl5TXpVNU5UbGFNRE14Q3pBSkJnTlZCQVlUQWxWVE1SWXdGQVlEVlFRS0V3MU1aWFFuY3lCRgpibU55ZVhCME1Rd3dDZ1lEVlFRREV3TlNNVEF3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUURQVitYbXhGUVM3YlJIL3NrbldIWkdVQ2lNSFQ2STN3V2QxYlVZS2IzZHRWcS8rdmJPbzc2dkFDRkwKWWxwYVBBRXZ4VmdEOW9uL2poRkQ2OEcxNEJRSGxvOXZIOWZudW9FNUNYVmx0OEt2R0ZzM0ppam5vL1FISzIwYQovNnRZdkpXdVFQL3B5MWZFdFZ0L2VBMFlZYndYNTFUR3UwbVJ6VzRZMFlDRjdxWmxOcngwNnJ4UVRPcjhJZk00CkZwT1V1ckRUYXpnR3pSWVNlc3BTZGNpdGRyTENuRjJZUlZ4dllYdkdMZTQ4RTFLR0FkbFg1amdjMzQyMUg1S1IKbXVkS0hNeEZxSEpWOExEbW93ZnMvYWNiWnA0L1NJdHhoSEZZeVRyNjcxN3lXMFFyUEhUbmo3Skh3UWRxelpxMwpEWmIzRW9FbVVWUUs3R0gyOS9YaThvcklsUTJOQWdNQkFBR2pnZmd3Z2ZVd0RnWURWUjBQQVFIL0JBUURBZ0dHCk1CMEdBMVVkSlFRV01CUUdDQ3NHQVFVRkJ3TUNCZ2dyQmdFRkJRY0RBVEFTQmdOVkhSTUJBZjhFQ0RBR0FRSC8KQWdFQU1CMEdBMVVkRGdRV0JCUzd2TU5IcGVTOHFjYkRwSElNRUkyaU5lSEk2REFmQmdOVkhTTUVHREFXZ0JSNQp0Rm5tZTdibDVBRnpnQWlJeUJwWTl1bWJiakF5QmdnckJnRUZCUWNCQVFRbU1DUXdJZ1lJS3dZQkJRVUhNQUtHCkZtaDBkSEE2THk5NE1TNXBMbXhsYm1OeUxtOXlaeTh3RXdZRFZSMGdCQXd3Q2pBSUJnWm5nUXdCQWdFd0p3WUQKVlIwZkJDQXdIakFjb0JxZ0dJWVdhSFIwY0RvdkwzZ3hMbU11YkdWdVkzSXViM0puTHpBTkJna3Foa2lHOXcwQgpBUXNGQUFPQ0FnRUFrckhuUVRmcmVaMkI1czNpSmVFNklPbVFSSldqZ1Z6UHcxMzl2YUJ3MWJHV0tDSUwwdklvCnp3em4xT1pEakNRaUhjRkNrdEVKcjU5TDlNaHdUeUFXc1ZyZEFmWWYrQjloYXhRbnNIS05ZNjd1NHM1THp6ZmQKdTZQVXplZXRVSzI5ditQc1BtSTJjSmt4cCtpTjNlcGk0aEt1OVp6VVBTd01xdENjZWI3cVBWeEVicFl4WTFwOQoxbjVQSktCTEJYOWViOUxVNmw4elN4UFdWN2JLM2xHNFhhTUpnblQ5eDNpZXM3bXNGdHBLSzViRHRvdGlqL2wwCkdhS2VBOTdwYjV1d0Q5S2dXdmFGWE1JRXQ4alZUakxFdndSZHZDbjI5NEdQREYwOFU4bEFrSXY3dGdobHVhUWgKMVFubEU0U0VONExPRUNqOGRzSUdKWHBHVWszYVUzS2tKejlpY0t5K2FVZ0ErMmNQMjF1aDZOY0RJUzNYeWZhWgpRam1EUTk5M0NoSUk4U1hXdXBRWlZCaUlwY1dPNFJxWmszbHI3Qno1TVVDd3pESUEzNTllNTdTU3E1Q0NrWTBOCjRCNlZ1bGs3TGt0ZndyZEdOVkk1QnNDOXFxeFN3U0tnUkplWjl3eWdJYWVoYkhGSEZoY0JhTURLcGlabEJIeXoKcnNubmxGWENiNXM4SEtuNUxzVWdHdkIyNEw3c0dOWlAyQ1g3ZGhIb3YrWWhEK2pvekxXMnA5VzQ5NTlCejJFaQpSbXFEdG1pWExuenFUcFhiSStzdXlDc29oS1JnNlVuMFJDNDcrY3BpVndIaVhaQVcrY244ZWlOSWpxYlZnWEx4CktQcGR6dnZ0VG5PUGxDN1NRWlNZbWR1bnIzQmY5Yjc3QWlDL1ppZHN0SzM2ZFJJTEt6N09BNTQ9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/alt-names: n8n-webhook.locke.cz
    cert-manager.io/certificate-name: n8n-webhook-tls
    cert-manager.io/common-name: n8n-webhook.locke.cz
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: cert-manager.io
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: letsencrypt-cloudflare
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
  name: n8n-webhook-tls
  namespace: n8n
type: kubernetes.io/tls
