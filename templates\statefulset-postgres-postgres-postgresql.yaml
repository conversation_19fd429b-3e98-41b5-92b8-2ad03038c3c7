﻿apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "mychart.fullname" . }}-postgresql
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: postgresql
spec:
  serviceName: {{ include "mychart.fullname" . }}-postgresql-hl
  replicas: {{ .Values.postgresql.replicaCount }}
  selector:
    matchLabels:
      {{- include "mychart.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: postgresql
  template:
    metadata:
      labels:
        {{- include "mychart.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: postgresql
    spec:
      {{- if .Values.postgresql.serviceAccount.create }}
      serviceAccountName: {{ include "mychart.fullname" . }}-postgresql
      {{- end }}
      {{- with .Values.postgresql.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: postgresql
        image: "{{ .Values.postgresql.image.repository }}:{{ .Values.postgresql.image.tag }}"
        imagePullPolicy: {{ .Values.postgresql.image.pullPolicy }}
        ports:
        - name: tcp-postgresql
          containerPort: 5432
          protocol: TCP
        env:
        - name: BITNAMI_DEBUG
          value: "{{ .Values.postgresql.debug }}"
        - name: POSTGRESQL_PORT_NUMBER
          value: "{{ .Values.postgresql.service.port }}"
        - name: POSTGRESQL_VOLUME_DIR
          value: /bitnami/postgresql
        - name: PGDATA
          value: /bitnami/postgresql/data
        - name: POSTGRES_USER
          value: {{ .Values.n8n.database.user }}
        - name: POSTGRES_PASSWORD_FILE
          value: /opt/bitnami/postgresql/secrets/password
        - name: POSTGRES_POSTGRES_PASSWORD_FILE
          value: /opt/bitnami/postgresql/secrets/postgres-password
        - name: POSTGRES_DATABASE
          value: {{ .Values.n8n.database.name }}
        - name: POSTGRESQL_ENABLE_LDAP
          value: "no"
        - name: POSTGRESQL_ENABLE_TLS
          value: "no"
        - name: POSTGRESQL_LOG_HOSTNAME
          value: "false"
        - name: POSTGRESQL_LOG_CONNECTIONS
          value: "false"
        - name: POSTGRESQL_LOG_DISCONNECTIONS
          value: "false"
        - name: POSTGRESQL_PGAUDIT_LOG_CATALOG
          value: "off"
        - name: POSTGRESQL_CLIENT_MIN_MESSAGES
          value: error
        - name: POSTGRESQL_SHARED_PRELOAD_LIBRARIES
          value: pgaudit
        {{- with .Values.postgresql.livenessProbe }}
        livenessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.postgresql.readinessProbe }}
        readinessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.postgresql.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.postgresql.securityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        volumeMounts:
        - mountPath: /tmp
          name: empty-dir
          subPath: tmp-dir
        - mountPath: /opt/bitnami/postgresql/conf
          name: empty-dir
          subPath: app-conf-dir
        - mountPath: /opt/bitnami/postgresql/tmp
          name: empty-dir
          subPath: app-tmp-dir
        - mountPath: /opt/bitnami/postgresql/secrets/
          name: postgresql-password
        - mountPath: /dev/shm
          name: dshm
        - mountPath: /bitnami/postgresql
          name: data
      {{- with .Values.postgresql.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.postgresql.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.postgresql.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
      - name: empty-dir
        emptyDir: {}
      - name: postgresql-password
        secret:
          secretName: {{ include "mychart.fullname" . }}-postgresql-secrets
      - name: dshm
        emptyDir:
          medium: Memory
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes:
      - ReadWriteOnce
      {{- if .Values.postgresql.persistence.storageClass }}
      storageClassName: {{ .Values.postgresql.persistence.storageClass }}
      {{- end }}
      resources:
        requests:
          storage: {{ .Values.postgresql.persistence.size }}
