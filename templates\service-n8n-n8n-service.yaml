﻿apiVersion: v1
kind: Service
metadata:
  name: {{ include "mychart.fullname" . }}-n8n
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: n8n
spec:
  type: {{ .Values.n8n.service.type }}
  ports:
  - port: {{ .Values.n8n.service.port }}
    targetPort: http
    protocol: TCP
    name: http
  selector:
    {{- include "mychart.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: n8n-main
