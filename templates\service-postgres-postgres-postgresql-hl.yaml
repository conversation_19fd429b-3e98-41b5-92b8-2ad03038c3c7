﻿apiVersion: v1
kind: Service
metadata:
  name: {{ include "mychart.fullname" . }}-postgresql-hl
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: postgresql
spec:
  type: ClusterIP
  clusterIP: None
  publishNotReadyAddresses: true
  ports:
  - name: tcp-postgresql
    port: {{ .Values.postgresql.service.port }}
    targetPort: tcp-postgresql
    protocol: TCP
  selector:
    {{- include "mychart.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: postgresql
