﻿apiVersion: v1
kind: Service
metadata:
  annotations:
    meta.helm.sh/release-name: postgres
    meta.helm.sh/release-namespace: postgres
  labels:
    app.kubernetes.io/component: primary
    app.kubernetes.io/instance: postgres
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/version: 17.5.0
    helm.sh/chart: postgresql-16.7.14
  name: postgres-postgresql-hl
  namespace: postgres
spec:
  clusterIP: None
  clusterIPs:
  - None
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: tcp-postgresql
    port: 5432
    targetPort: tcp-postgresql
  publishNotReadyAddresses: true
  selector:
    app.kubernetes.io/component: primary
    app.kubernetes.io/instance: postgres
    app.kubernetes.io/name: postgresql
