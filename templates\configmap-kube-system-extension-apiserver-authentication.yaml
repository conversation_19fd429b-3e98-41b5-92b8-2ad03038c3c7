﻿apiVersion: v1
data:
  client-ca-file: |
    -----BEGIN CERTIFICATE-----
    MIIDiTCCAnGgAwIBAgIRAKtR1Tnz3Lnbd5W1ftqJgbMwDQYJKoZIhvcNAQELBQAw
    XjEPMA0GA1UEAwwGSzhzIENBMQswCQYDVQQGEwJVUzEPMA0GA1UEBwwGQXVzdGlu
    MQ8wDQYDVQQKDAZPcmFjbGUxDDAKBgNVBAsMA09jaTEOMAwGA1UECAwFVGV4YXMw
    HhcNMjUwNjMwMDY0MTU2WhcNMzAwNjMwMDY0MTU2WjBeMQ8wDQYDVQQDDAZLOHMg
    Q0ExCzAJBgNVBAYTAlVTMQ8wDQYDVQQHDAZBdXN0aW4xDzANBgNVBAoMBk9yYWNs
    ZTEMMAoGA1UECwwDT2NpMQ4wDAYDVQQIDAVUZXhhczCCASIwDQYJKoZIhvcNAQEB
    BQADggEPADCCAQoCggEBAJZQ5MzHfXqEMvyxPDRtfx+jHgsRltEqko0WtbbaY9YO
    DQ0aGhZBG+J1VUHaPgioBwPOXNqVfAGtbSYp/SpIVDOj94JCpHaLLMc8+U/GhzJw
    cDwYpOc9CypF9Z8BeyawF1xnxKm2K8t1T/F+fRG3t7zEG0JD7QLsyp7R16YsjFcB
    ytq/gyc0pyM2aL6FXqs0mx4tgzLL0HkLClsqLTJcVl6cYxPCS3HioIK1Wkt71UhW
    JyttZ+1wreqVKfCBqrkGeD6kXPntUukQgZNI8Ay5ExHv1Ce+t4jAUFo+yfIMI7PG
    8yEPizO4gpV4MMWanGUp1FkpLDmUMCOnYa6dySQUtV8CAwEAAaNCMEAwDwYDVR0T
    AQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYwHQYDVR0OBBYEFM/ubNBC2SksNxZj
    eoE7GbB51pR+MA0GCSqGSIb3DQEBCwUAA4IBAQAu/Xj+zDV6k89JfUMifNJFMLG+
    ZYGSLPLKYAHdnb21tXauXJkOZCMlJhJgyEE30mveWbjb4EiA0Kz82mFB/YbTznDp
    RNRy0XDnPNInX3WeUIEoMCwKrCb0kfhXWhH/UZizVB28Ny2rq9vq5EDGOxc6dv3h
    3jEz+iGdp5WOL0CRwxc5qVdSl+FXTxPk/icEKbkDY5VQ5qYZghV5miLC0jDkJ3T5
    g4RIA00iT/PmFfhAyGiW2t5eQjpHDe29frzU1C6/JY3fzfBiRLZGfP53jrkaEWPk
    F72J/RSVehOWI8BPUMMvssFGCwlci6NlDDjqG2xQsOcMDJud+14EIq4AyOrq
    -----END CERTIFICATE-----
  requestheader-allowed-names: '["aggregator"]'
  requestheader-client-ca-file: |
    -----BEGIN CERTIFICATE-----
    MIIDnjCCAoagAwIBAgIQHCTTlUm6Pyo+xIYHC3180DANBgkqhkiG9w0BAQsFADBp
    MRowGAYDVQQDDBFLOFMgQWdncmVnYXRvciBDQTELMAkGA1UEBhMCVVMxDzANBgNV
    BAcMBkF1c3RpbjEPMA0GA1UECgwGT3JhY2xlMQwwCgYDVQQLDANPY2kxDjAMBgNV
    BAgMBVRleGFzMB4XDTI1MDYzMDA2NDE1NloXDTMwMDYzMDA2NDE1NlowaTEaMBgG
    A1UEAwwRSzhTIEFnZ3JlZ2F0b3IgQ0ExCzAJBgNVBAYTAlVTMQ8wDQYDVQQHDAZB
    dXN0aW4xDzANBgNVBAoMBk9yYWNsZTEMMAoGA1UECwwDT2NpMQ4wDAYDVQQIDAVU
    ZXhhczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJQB4+/Mo5KpwqGP
    +YwlOcIo35zSp8363CHx007v0fR2XwVxweY0xhcPyQ94Y1m1hEzSYNU2rYdYntXD
    OLWZYehGVqFA+XKXXC0io6o9cdEiNyvmeJ5C+HvGch8jSSqoTSwZQ7LRGdJIUYWp
    4nChIDScilsN+sorUi+okCgQIGbxlE/k1jpPBzdiiAM12IVDc7GRJCyrzpSmVU5G
    qwWsNvfkB8jMgJcwik53F91SHp214U7Qdul8ezfKXLVizFOKArIkaSx/kImKfz9w
    fEgy5Tc4mH7BPe9SMs67AlEUb31ZljzYeZSdP3VhyR2wOdT7rKRbhIuHhLSXfrFg
    th1FyAUCAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
    HQYDVR0OBBYEFEmWtNRBDnjju8VRDIwNkqH9yQMyMA0GCSqGSIb3DQEBCwUAA4IB
    AQBjzlOLyv4BgptMemEvkTSg2uS1wmQ1Y504Mr7hT3zARuROXwpsqB5vzsoIS45a
    0oZtamymluZJIdJ0y0LPqAozs4yxJuyc4tSTRQFamo0IYrbntsazcTlF+37++pQO
    UGtSgPUSDGEYy/DhRuN3F8EhbQpQxAX38QZOz+8PUe8s6UeHOql4WZ3uQNnsAtqe
    FdBlfN8e5KTbCsCTtK7c0UFYzOTllkvD2R1k7ThQF3+96FPluspBJNZD75gr3nuu
    XKC/XMxdM8WWu7guCW2ljFSpBbUslclVehsC4BsuNKnRkoIHxV32MDWv/x4VLoT+
    Ux2jKyj5zzR09rnjiJRD3TYn
    -----END CERTIFICATE-----
  requestheader-extra-headers-prefix: '["X-Remote-Extra-"]'
  requestheader-group-headers: '["X-Remote-Group"]'
  requestheader-username-headers: '["X-Remote-User"]'
kind: ConfigMap
metadata:
  name: extension-apiserver-authentication
  namespace: kube-system
