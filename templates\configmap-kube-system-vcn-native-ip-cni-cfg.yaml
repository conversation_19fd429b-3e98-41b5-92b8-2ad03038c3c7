﻿apiVersion: v1
data:
  cni-conf.json: '{"name":"oci","cniVersion":"0.3.1","plugins":[{"cniVersion":"0.3.1","type":"oci-ipvlan","mode":"l2","ipam":{"type":"oci-ipam"}},{"cniVersion":"0.3.1","type":"oci-ptp","containerInterface":"ptp-veth0","mtu":9000}]}'
  ip6tables: |-
    #!/bin/sh
    if [ -x /host/sbin/ip6tables ]; then
    chroot /host /sbin/ip6tables "$@"
    elif [ -x /host/usr/local/sbin/ip6tables ]; then
    chroot /host /usr/local/sbin/ip6tables "$@"
    elif [ -x /host/bin/ip6tables ]; then
    chroot /host /bin/ip6tables "$@"
    elif [ -x /host/usr/local/bin/ip6tables ]; then
    chroot /host /usr/local/bin/ip6tables "$@"
    else
    chroot /host ip6tables "$@"
    fi
  iptables: |-
    #!/bin/sh
    if [ -x /host/sbin/iptables ]; then
    chroot /host /sbin/iptables "$@"
    elif [ -x /host/usr/local/sbin/iptables ]; then
    chroot /host /usr/local/sbin/iptables "$@"
    elif [ -x /host/bin/iptables ]; then
    chroot /host /bin/iptables "$@"
    elif [ -x /host/usr/local/bin/iptables ]; then
    chroot /host /usr/local/bin/iptables "$@"
    else
    chroot /host iptables "$@"
    fi
kind: ConfigMap
metadata:
  labels:
    app: vcn-native-ip-cni
    tier: node
  name: vcn-native-ip-cni-cfg
  namespace: kube-system
