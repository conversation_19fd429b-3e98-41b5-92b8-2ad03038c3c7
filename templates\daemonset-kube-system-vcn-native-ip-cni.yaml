﻿apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    deprecated.daemonset.template.generation: "1"
  labels:
    app: vcn-native-ip-cni
    tier: node
  name: vcn-native-ip-cni
  namespace: kube-system
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: vcn-native-ip-cni
  template:
    metadata:
      annotations:
        checksum/config: 4934a973fed4c4b4
        version_hash: "-793777849"
      creationTimestamp: null
      labels:
        app: vcn-native-ip-cni
        tier: node
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: oci.oraclecloud.com/vcn-native-ip-cni
                operator: In
                values:
                - "true"
              - key: node-role.kubernetes.io/virtual-node
                operator: DoesNotExist
      containers:
      - command:
        - /bin/oci-npn
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: CNI_COPY_FILES
          value: "true"
        - name: PATH
          value: /hostIptables:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/host/usr/bin:/host/sbin
        - name: HAS_INIT_CONTAINER
          value: "false"
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-vcn-native-ip-cni-plugin:2.2.8-5@sha256:8807b16bbf2be804a724c680d4c7ff7f813d1d44774e64da2d900a38a79dba4d
        imagePullPolicy: IfNotPresent
        name: install-cni-ips
        securityContext:
          privileged: true
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /opt/cni/bin
          name: cni-plugin
        - mountPath: /dev/shm
          name: ip-dir
        - mountPath: /etc/cni/net.d
          name: cni
        - mountPath: /etc/oci-cni
          name: cni-cfg
        - mountPath: /host
          name: host-root
        - mountPath: /hostIptables/iptables
          name: chroot-iptables
          subPath: iptables
        - mountPath: /hostIptables/ip6tables
          name: chroot-ip6tables
          subPath: ip6tables
      - command:
        - /bin/bash
        - -ce
        - if [ -x /bin/oci-cni-device-plugin ]; then exec /bin/oci-cni-device-plugin;
          else echo "Application resources not supported with this version of VCN
          Native IP CNI; skip device plugin creation" >&2; trap "exit 0" TERM INT;
          sleep infinity & wait; fi
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: HAS_INIT_CONTAINER
          value: "false"
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-vcn-native-ip-cni-plugin:2.2.8-5@sha256:8807b16bbf2be804a724c680d4c7ff7f813d1d44774e64da2d900a38a79dba4d
        imagePullPolicy: IfNotPresent
        name: oci-cni-device-plugin
        securityContext:
          privileged: true
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /var/lib/kubelet/device-plugins
          name: kubelet-device-plugins
      dnsPolicy: ClusterFirst
      hostNetwork: true
      initContainers:
      - command:
        - /bin/bash
        - -ce
        - echo Attempting to reach Kubernetes API server at ${KUBERNETES_SERVICE_HOST}:${KUBERNETES_SERVICE_PORT};
          timeout 30 curl -ksSvo /dev/null --connect-timeout 5 --retry-delay 5 --retry
          999 https://${KUBERNETES_SERVICE_HOST}:${KUBERNETES_SERVICE_PORT}/api/v1/nodes
          || true
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-vcn-native-ip-cni-plugin:2.2.8-5@sha256:8807b16bbf2be804a724c680d4c7ff7f813d1d44774e64da2d900a38a79dba4d
        imagePullPolicy: IfNotPresent
        name: oci-cni-init
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      serviceAccount: vcn-native-ip-cni
      serviceAccountName: vcn-native-ip-cni
      terminationGracePeriodSeconds: 30
      tolerations:
      - operator: Exists
      volumes:
      - hostPath:
          path: /opt/cni/bin
          type: ""
        name: cni-plugin
      - hostPath:
          path: /etc/cni/net.d
          type: ""
        name: cni
      - hostPath:
          path: /dev/shm
          type: ""
        name: ip-dir
      - hostPath:
          path: /var/lib/kubelet/device-plugins
          type: Directory
        name: kubelet-device-plugins
      - configMap:
          defaultMode: 420
          name: vcn-native-ip-cni-cfg
        name: cni-cfg
      - configMap:
          defaultMode: 493
          items:
          - key: iptables
            path: iptables
          name: vcn-native-ip-cni-cfg
        name: chroot-iptables
      - configMap:
          defaultMode: 493
          items:
          - key: ip6tables
            path: ip6tables
          name: vcn-native-ip-cni-cfg
        name: chroot-ip6tables
      - hostPath:
          path: /
          type: Directory
        name: host-root
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
