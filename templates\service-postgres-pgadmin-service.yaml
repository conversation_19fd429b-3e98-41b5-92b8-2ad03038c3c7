﻿{{- if .Values.pgadmin.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "mychart.fullname" . }}-pgadmin
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: pgadmin
spec:
  type: {{ .Values.pgadmin.service.type }}
  ports:
  - port: {{ .Values.pgadmin.service.port }}
    targetPort: http
    protocol: TCP
    name: http
  selector:
    {{- include "mychart.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: pgadmin
{{- end }}
