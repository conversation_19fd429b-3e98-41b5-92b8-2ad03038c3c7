﻿{{- if .Values.n8n.ingress.admin.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "mychart.fullname" . }}-n8n-admin
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: n8n-admin
  {{- with .Values.n8n.ingress.admin.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.n8n.ingress.admin.className }}
  ingressClassName: {{ .Values.n8n.ingress.admin.className }}
  {{- end }}
  {{- if .Values.n8n.ingress.admin.tls }}
  tls:
    {{- range .Values.n8n.ingress.admin.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.n8n.ingress.admin.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if .pathType }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              service:
                name: {{ include "mychart.fullname" $ }}-n8n
                port:
                  number: {{ $.Values.n8n.service.port }}
          {{- end }}
    {{- end }}
{{- end }}
