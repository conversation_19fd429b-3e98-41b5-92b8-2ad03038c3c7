﻿apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare
    kubernetes.io/timestamp: 06/30/2025 11:16:48
    nginx.ingress.kubernetes.io/whitelist-source-range: *********/24,*********/24
  name: n8n-admin-ingress
  namespace: n8n
spec:
  ingressClassName: nginx
  rules:
  - host: n8n-admin.locke.cz
    http:
      paths:
      - backend:
          service:
            name: n8n-service
            port:
              number: 5678
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - n8n-admin.locke.cz
    secretName: n8n-admin-tls
