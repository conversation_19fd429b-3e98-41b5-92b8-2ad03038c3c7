﻿apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    deprecated.daemonset.template.generation: "1"
  labels:
    k8s-app: kube-proxy
  name: kube-proxy
  namespace: kube-system
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      k8s-app: kube-proxy
  template:
    metadata:
      annotations:
        checksum/config: aa2a7e9307873efb
        version_hash: "136586374"
      creationTimestamp: null
      labels:
        k8s-app: kube-proxy
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-role.kubernetes.io/virtual-node
                operator: DoesNotExist
      containers:
      - command:
        - /usr/local/bin/kube-proxy
        - --config=/var/lib/kube-proxy/config.conf
        - --hostname-override=$(NODE_NAME)
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: PATH
          value: /hostIptables:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/host/usr/bin:/host/sbin
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-kube-proxy@sha256:37532fcb9ce58ec1398360d55ee55c45b73a85f841be631f6e94595a82a518d7
        imagePullPolicy: IfNotPresent
        name: kube-proxy
        securityContext:
          allowPrivilegeEscalation: true
          privileged: true
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /var/lib/kube-proxy
          name: kube-proxy
        - mountPath: /run/xtables.lock
          name: xtables-lock
        - mountPath: /lib/modules
          name: lib-modules
          readOnly: true
        - mountPath: /host
          name: host-root
        - mountPath: /hostIptables/iptables
          name: chroot-iptables
          subPath: iptables
        - mountPath: /hostIptables/iptables-save
          name: chroot-iptables
          subPath: iptables-save
        - mountPath: /hostIptables/iptables-restore
          name: chroot-iptables
          subPath: iptables-restore
        - mountPath: /hostIptables/ip6tables
          name: chroot-iptables
          subPath: ip6tables
        - mountPath: /hostIptables/ip6tables-save
          name: chroot-iptables
          subPath: ip6tables-save
        - mountPath: /hostIptables/ip6tables-restore
          name: chroot-iptables
          subPath: ip6tables-restore
      dnsPolicy: ClusterFirst
      hostNetwork: true
      initContainers:
      - command:
        - sh
        - /var/lib/kube-proxy-config/kube_proxy_init.sh
        image: eu-frankfurt-1.ocir.io/axoxdievda5j/oke-public-kube-proxy@sha256:37532fcb9ce58ec1398360d55ee55c45b73a85f841be631f6e94595a82a518d7
        imagePullPolicy: IfNotPresent
        name: init-kube-proxy
        securityContext:
          allowPrivilegeEscalation: true
          privileged: true
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /var/lib/kube-proxy
          name: kube-proxy
        - mountPath: /var/lib/kube-proxy-config
          name: kube-proxy-config-volume
        - mountPath: /var/lib/kube-proxy-k8s-version
          name: kube-proxy-k8s-version-volume
      nodeSelector:
        beta.kubernetes.io/os: linux
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        runAsNonRoot: false
        runAsUser: 0
      serviceAccount: kube-proxy
      serviceAccountName: kube-proxy
      terminationGracePeriodSeconds: 30
      tolerations:
      - key: CriticalAddonsOnly
        operator: Exists
      - operator: Exists
      volumes:
      - name: kube-proxy
      - configMap:
          defaultMode: 420
          name: kube-proxy
        name: kube-proxy-config-volume
      - hostPath:
          path: /run/xtables.lock
          type: FileOrCreate
        name: xtables-lock
      - hostPath:
          path: /lib/modules
          type: ""
        name: lib-modules
      - hostPath:
          path: /etc/oke/oke-k8s-version
          type: ""
        name: kube-proxy-k8s-version-volume
      - configMap:
          defaultMode: 493
          items:
          - key: iptables
            path: iptables
          - key: iptables-save
            path: iptables-save
          - key: iptables-restore
            path: iptables-restore
          - key: ip6tables
            path: ip6tables
          - key: ip6tables-save
            path: ip6tables-save
          - key: ip6tables-restore
            path: ip6tables-restore
          name: oci-iptables-kubeproxy
        name: chroot-iptables
      - hostPath:
          path: /
          type: Directory
        name: host-root
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
