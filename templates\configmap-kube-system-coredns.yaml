﻿apiVersion: v1
data:
  Corefile: |-
    .:53 {
      errors
      health {
        lameduck 5s
      }
      ready
      kubernetes cluster.local in-addr.arpa ip6.arpa {
        pods insecure
        fallthrough in-addr.arpa ip6.arpa
      }
      prometheus :9153
      forward . /etc/resolv.conf
      cache 30
      loop
      reload
      loadbalance
    }
    import custom/*.server
kind: ConfigMap
metadata:
  name: coredns
  namespace: kube-system
