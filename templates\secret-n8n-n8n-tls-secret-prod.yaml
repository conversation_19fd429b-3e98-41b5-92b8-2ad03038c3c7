﻿apiVersion: v1
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUU5akNDQTk2Z0F3SUJBZ0lTQmoyYklKY0VCOEptT0dSc0x5RlVxOWhjTUEwR0NTcUdTSWIzRFFFQkN3VUEKTURNeEN6QUpCZ05WQkFZVEFsVlRNUll3RkFZRFZRUUtFdzFNWlhRbmN5QkZibU55ZVhCME1Rd3dDZ1lEVlFRRApFd05TTVRBd0hoY05NalV3TmpJeU1UY3dNVE00V2hjTk1qVXdPVEl3TVRjd01UTTNXakFYTVJVd0V3WURWUVFECkV3eHVPRzR1Ykc5amEyVXVZM293Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRREYKdEdyZWNXNzlVbVJLWCtoV216SjNBMGd2NEhMZEZBaXg4TXF0b0ZOdjc2WVRLay9rNUwzUnpXeGV3dTdVMVRMUwpLZFNOWWdsSzRHZjZXeWtGVFYrNC9xRFpNYTJtTHlIdE1KMXphc3oxbHVUWGRrMnUyU2RFb0pXUzIwTmQyT3BoClJsSk5halV1L2JzU2NaRElRNERGeDRlYVlrczJCWjZWTnc1VU1OdHRkL1dQYVNnUjdjdFhtQThGWU11OWlLb24KeGh0SVpIa1R6RUhGOHE5TkJKQlp0NGkwQ0hPQjBkNEFObmpDdnN0eUNoTWtDV3ZSMHppd0VBSXMxeStaUVBHawpMcDkzMjJ0S2FnNTExQXpObFlMdGgrK2pEdnJrL3I0V3ZRU0VLcHI5NWZGN0tVdEFxYm5NN0QvMUZHV244alI5CklpWTVtVWZkVWt0eWYwRjZRaDVyQWdNQkFBR2pnZ0llTUlJQ0dqQU9CZ05WSFE4QkFmOEVCQU1DQmFBd0hRWUQKVlIwbEJCWXdGQVlJS3dZQkJRVUhBd0VHQ0NzR0FRVUZCd01DTUF3R0ExVWRFd0VCL3dRQ01BQXdIUVlEVlIwTwpCQllFRkQ0RDg1a29TME9CMWIwS2xva2lnKzR4MjVsRk1COEdBMVVkSXdRWU1CYUFGTHU4dzBlbDVMeXB4c09rCmNnd1FqYUkxNGNqb01ETUdDQ3NHQVFVRkJ3RUJCQ2N3SlRBakJnZ3JCZ0VGQlFjd0FvWVhhSFIwY0RvdkwzSXgKTUM1cExteGxibU55TG05eVp5OHdGd1lEVlIwUkJCQXdEb0lNYmpodUxteHZZMnRsTG1ONk1CTUdBMVVkSUFRTQpNQW93Q0FZR1o0RU1BUUlCTUM0R0ExVWRId1FuTUNVd0k2QWhvQitHSFdoMGRIQTZMeTl5TVRBdVl5NXNaVzVqCmNpNXZjbWN2T1RNdVkzSnNNSUlCQmdZS0t3WUJCQUhXZVFJRUFnU0I5d1NCOUFEeUFIY0FmVmtlRXVGNEtuc2MKWVdkOFh2MzQwSWRjRktCT2xaNjVBeS9aRG93dWViZ0FBQUdYbU13MFlnQUFCQU1BU0RCR0FpRUF1YVZ1aVhVbgp5WW14WVcwWWNxeWxCbzAxNnZGOHlxTldiNDhjbVEzTitFSUNJUURSV1pBQjhlNUEybXRlRDhidXZWTm80MFdzCjVUZW5WR0RydWs5bituZzEyd0IzQU4zY3lqU1YxK0VXQmVlVk12ckhuL2c5SEZEZjJ3QTZGQkoyQ2l5c3U4Z3EKQUFBQmw1ak1OSkVBQUFRREFFZ3dSZ0loQU5udHNqbDVjVTZKOXd1b1M3NHNic2dYSXJnWDZEamIveUxjU0dJUwpMT2x1QWlFQWx2M2oxeE9ZckwzcjhTVzJNQlZLTWVueWhxKzFkOTM1bFlmUlVrTGh4bUl3RFFZSktvWklodmNOCkFRRUxCUUFEZ2dFQkFFVWViZ2F3NDM5eENaOWM3NWpOVUdGWUpON1ZiM2gxUEM3YlZoTzBNMlFMV1hkMkJEZVkKb213bG9adVdhUXFmWWZxVFROSzVpS1UxMGxrS2dlT2JaMm5KemExZlRHR1laNGZmK2Z6OXBrK1RzbVhaT1hBbApoaWt2c1ZYb1RxQXNSdHdiQ05iUStGZnA4aGh4QzF3NzlOa0ZsWGZCQW41b0h2SGJ2K2NrT2VNMzVsZ3Yvd3BwCkRrcUVoMEhnVDNWVGhsT1I4UmdYK28yRmVqMnhVclZ2aHdzZThoUVZjMERIbFdWVGZFQ0NVN0o5b0FCZU9iT0gKY0ZsTERBaCtRLy94TTJwU01OYVhYeGZKSDl6dkc0dFphVkUxZk83VWR2ZkJ1WVZmRVI3em9zY3p2aE0wS2hUbgp1R0lpNnZoeWdDOVg2eHNVbUNtV0ZhRzF6L2QxYUVnV1dYUT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQotLS0tLUJFR0lOIENFUlRJRklDQVRFLS0tLS0KTUlJRkJUQ0NBdTJnQXdJQkFnSVFTNmhTay9lYUw2SnpCa3VvQkkxMTBEQU5CZ2txaGtpRzl3MEJBUXNGQURCUApNUXN3Q1FZRFZRUUdFd0pWVXpFcE1DY0dBMVVFQ2hNZ1NXNTBaWEp1WlhRZ1UyVmpkWEpwZEhrZ1VtVnpaV0Z5ClkyZ2dSM0p2ZFhBeEZUQVRCZ05WQkFNVERFbFRVa2NnVW05dmRDQllNVEFlRncweU5EQXpNVE13TURBd01EQmEKRncweU56QXpNVEl5TXpVNU5UbGFNRE14Q3pBSkJnTlZCQVlUQWxWVE1SWXdGQVlEVlFRS0V3MU1aWFFuY3lCRgpibU55ZVhCME1Rd3dDZ1lEVlFRREV3TlNNVEF3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUURQVitYbXhGUVM3YlJIL3NrbldIWkdVQ2lNSFQ2STN3V2QxYlVZS2IzZHRWcS8rdmJPbzc2dkFDRkwKWWxwYVBBRXZ4VmdEOW9uL2poRkQ2OEcxNEJRSGxvOXZIOWZudW9FNUNYVmx0OEt2R0ZzM0ppam5vL1FISzIwYQovNnRZdkpXdVFQL3B5MWZFdFZ0L2VBMFlZYndYNTFUR3UwbVJ6VzRZMFlDRjdxWmxOcngwNnJ4UVRPcjhJZk00CkZwT1V1ckRUYXpnR3pSWVNlc3BTZGNpdGRyTENuRjJZUlZ4dllYdkdMZTQ4RTFLR0FkbFg1amdjMzQyMUg1S1IKbXVkS0hNeEZxSEpWOExEbW93ZnMvYWNiWnA0L1NJdHhoSEZZeVRyNjcxN3lXMFFyUEhUbmo3Skh3UWRxelpxMwpEWmIzRW9FbVVWUUs3R0gyOS9YaThvcklsUTJOQWdNQkFBR2pnZmd3Z2ZVd0RnWURWUjBQQVFIL0JBUURBZ0dHCk1CMEdBMVVkSlFRV01CUUdDQ3NHQVFVRkJ3TUNCZ2dyQmdFRkJRY0RBVEFTQmdOVkhSTUJBZjhFQ0RBR0FRSC8KQWdFQU1CMEdBMVVkRGdRV0JCUzd2TU5IcGVTOHFjYkRwSElNRUkyaU5lSEk2REFmQmdOVkhTTUVHREFXZ0JSNQp0Rm5tZTdibDVBRnpnQWlJeUJwWTl1bWJiakF5QmdnckJnRUZCUWNCQVFRbU1DUXdJZ1lJS3dZQkJRVUhNQUtHCkZtaDBkSEE2THk5NE1TNXBMbXhsYm1OeUxtOXlaeTh3RXdZRFZSMGdCQXd3Q2pBSUJnWm5nUXdCQWdFd0p3WUQKVlIwZkJDQXdIakFjb0JxZ0dJWVdhSFIwY0RvdkwzZ3hMbU11YkdWdVkzSXViM0puTHpBTkJna3Foa2lHOXcwQgpBUXNGQUFPQ0FnRUFrckhuUVRmcmVaMkI1czNpSmVFNklPbVFSSldqZ1Z6UHcxMzl2YUJ3MWJHV0tDSUwwdklvCnp3em4xT1pEakNRaUhjRkNrdEVKcjU5TDlNaHdUeUFXc1ZyZEFmWWYrQjloYXhRbnNIS05ZNjd1NHM1THp6ZmQKdTZQVXplZXRVSzI5ditQc1BtSTJjSmt4cCtpTjNlcGk0aEt1OVp6VVBTd01xdENjZWI3cVBWeEVicFl4WTFwOQoxbjVQSktCTEJYOWViOUxVNmw4elN4UFdWN2JLM2xHNFhhTUpnblQ5eDNpZXM3bXNGdHBLSzViRHRvdGlqL2wwCkdhS2VBOTdwYjV1d0Q5S2dXdmFGWE1JRXQ4alZUakxFdndSZHZDbjI5NEdQREYwOFU4bEFrSXY3dGdobHVhUWgKMVFubEU0U0VONExPRUNqOGRzSUdKWHBHVWszYVUzS2tKejlpY0t5K2FVZ0ErMmNQMjF1aDZOY0RJUzNYeWZhWgpRam1EUTk5M0NoSUk4U1hXdXBRWlZCaUlwY1dPNFJxWmszbHI3Qno1TVVDd3pESUEzNTllNTdTU3E1Q0NrWTBOCjRCNlZ1bGs3TGt0ZndyZEdOVkk1QnNDOXFxeFN3U0tnUkplWjl3eWdJYWVoYkhGSEZoY0JhTURLcGlabEJIeXoKcnNubmxGWENiNXM4SEtuNUxzVWdHdkIyNEw3c0dOWlAyQ1g3ZGhIb3YrWWhEK2pvekxXMnA5VzQ5NTlCejJFaQpSbXFEdG1pWExuenFUcFhiSStzdXlDc29oS1JnNlVuMFJDNDcrY3BpVndIaVhaQVcrY244ZWlOSWpxYlZnWEx4CktQcGR6dnZ0VG5PUGxDN1NRWlNZbWR1bnIzQmY5Yjc3QWlDL1ppZHN0SzM2ZFJJTEt6N09BNTQ9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  annotations:
    cert-manager.io/alt-names: n8n.locke.cz
    cert-manager.io/certificate-name: n8n-tls-secret-prod
    cert-manager.io/common-name: n8n.locke.cz
    cert-manager.io/ip-sans: ""
    cert-manager.io/issuer-group: cert-manager.io
    cert-manager.io/issuer-kind: ClusterIssuer
    cert-manager.io/issuer-name: letsencrypt-prod
    cert-manager.io/uri-sans: ""
  labels:
    controller.cert-manager.io/fao: "true"
  name: n8n-tls-secret-prod
  namespace: n8n
type: kubernetes.io/tls
