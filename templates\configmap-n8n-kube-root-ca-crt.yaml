﻿apiVersion: v1
data:
  ca.crt: |
    -----BEGIN CERTIFICATE-----
    MIIDiTCCAnGgAwIBAgIRAKtR1Tnz3Lnbd5W1ftqJgbMwDQYJKoZIhvcNAQELBQAw
    XjEPMA0GA1UEAwwGSzhzIENBMQswCQYDVQQGEwJVUzEPMA0GA1UEBwwGQXVzdGlu
    MQ8wDQYDVQQKDAZPcmFjbGUxDDAKBgNVBAsMA09jaTEOMAwGA1UECAwFVGV4YXMw
    HhcNMjUwNjMwMDY0MTU2WhcNMzAwNjMwMDY0MTU2WjBeMQ8wDQYDVQQDDAZLOHMg
    Q0ExCzAJBgNVBAYTAlVTMQ8wDQYDVQQHDAZBdXN0aW4xDzANBgNVBAoMBk9yYWNs
    ZTEMMAoGA1UECwwDT2NpMQ4wDAYDVQQIDAVUZXhhczCCASIwDQYJKoZIhvcNAQEB
    BQADggEPADCCAQoCggEBAJZQ5MzHfXqEMvyxPDRtfx+jHgsRltEqko0WtbbaY9YO
    DQ0aGhZBG+J1VUHaPgioBwPOXNqVfAGtbSYp/SpIVDOj94JCpHaLLMc8+U/GhzJw
    cDwYpOc9CypF9Z8BeyawF1xnxKm2K8t1T/F+fRG3t7zEG0JD7QLsyp7R16YsjFcB
    ytq/gyc0pyM2aL6FXqs0mx4tgzLL0HkLClsqLTJcVl6cYxPCS3HioIK1Wkt71UhW
    JyttZ+1wreqVKfCBqrkGeD6kXPntUukQgZNI8Ay5ExHv1Ce+t4jAUFo+yfIMI7PG
    8yEPizO4gpV4MMWanGUp1FkpLDmUMCOnYa6dySQUtV8CAwEAAaNCMEAwDwYDVR0T
    AQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYwHQYDVR0OBBYEFM/ubNBC2SksNxZj
    eoE7GbB51pR+MA0GCSqGSIb3DQEBCwUAA4IBAQAu/Xj+zDV6k89JfUMifNJFMLG+
    ZYGSLPLKYAHdnb21tXauXJkOZCMlJhJgyEE30mveWbjb4EiA0Kz82mFB/YbTznDp
    RNRy0XDnPNInX3WeUIEoMCwKrCb0kfhXWhH/UZizVB28Ny2rq9vq5EDGOxc6dv3h
    3jEz+iGdp5WOL0CRwxc5qVdSl+FXTxPk/icEKbkDY5VQ5qYZghV5miLC0jDkJ3T5
    g4RIA00iT/PmFfhAyGiW2t5eQjpHDe29frzU1C6/JY3fzfBiRLZGfP53jrkaEWPk
    F72J/RSVehOWI8BPUMMvssFGCwlci6NlDDjqG2xQsOcMDJud+14EIq4AyOrq
    -----END CERTIFICATE-----
kind: ConfigMap
metadata:
  annotations:
    kubernetes.io/description: Contains a CA bundle that can be used to verify the
      kube-apiserver when using internal endpoints such as the internal service IP
      or kubernetes.default.svc. No other usage is guaranteed across distributions
      of Kubernetes clusters.
  name: kube-root-ca.crt
  namespace: n8n
