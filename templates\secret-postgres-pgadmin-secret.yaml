﻿{{- if .Values.pgadmin.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "mychart.fullname" . }}-pgadmin-secrets
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
type: Opaque
data:
  PGADMIN_DEFAULT_EMAIL: {{ .Values.secrets.pgadminEmail | b64enc | quote }}
  PGADMIN_DEFAULT_PASSWORD: {{ .Values.secrets.pgadminPassword | b64enc | quote }}
{{- end }}
