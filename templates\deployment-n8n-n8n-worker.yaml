﻿apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "mychart.fullname" . }}-n8n-worker
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "mychart.labels" . | nindent 4 }}
    app.kubernetes.io/component: n8n-worker
spec:
  replicas: {{ .Values.n8n.worker.replicaCount }}
  selector:
    matchLabels:
      {{- include "mychart.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: n8n-worker
  template:
    metadata:
      labels:
        {{- include "mychart.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: n8n-worker
    spec:
      containers:
      - name: n8n
        image: "{{ .Values.n8n.worker.image.repository }}:{{ .Values.n8n.worker.image.tag }}"
        imagePullPolicy: {{ .Values.n8n.worker.image.pullPolicy }}
        env:
        - name: DB_TYPE
          value: {{ .Values.n8n.database.type }}
        - name: DB_POSTGRESDB_HOST
          value: {{ include "mychart.fullname" . }}-postgresql.{{ .Release.Namespace }}.svc.cluster.local
        - name: DB_POSTGRESDB_PORT
          value: "{{ .Values.postgresql.service.port }}"
        - name: DB_POSTGRESDB_DATABASE
          value: {{ .Values.n8n.database.name }}
        - name: DB_POSTGRESDB_USER
          value: {{ .Values.n8n.database.user }}
        - name: DB_POSTGRESDB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ include "mychart.fullname" . }}-n8n-secrets
              key: db-password
        - name: QUEUE_MODE
          value: worker
        - name: N8N_REDIS_HOST
          value: {{ include "mychart.fullname" . }}-redis.{{ .Release.Namespace }}.svc.cluster.local
        {{- with .Values.n8n.worker.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.n8n.worker.livenessProbe }}
        livenessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.n8n.worker.readinessProbe }}
        readinessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- with .Values.n8n.worker.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.n8n.worker.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.n8n.worker.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
